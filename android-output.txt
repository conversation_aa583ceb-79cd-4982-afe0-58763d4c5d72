2025-08-12 12:07:16,640 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-08-12 12:07:16,640 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-08-12 12:07:16,641 - app_android.config_android - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/android_data/test_cases
2025-08-12 12:07:16,641 - app_android.config_android - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports_android
2025-08-12 12:07:16,641 - app_android.config_android - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots_android
2025-08-12 12:07:16,642 - app_android.config_android - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/reference_images
2025-08-12 12:07:16,642 - app_android.config_android - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/android_data/test_suites
2025-08-12 12:07:16,643 - app_android.config_android - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports_android/suites
2025-08-12 12:07:16,643 - app_android.config_android - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/recordings_android
2025-08-12 12:07:16,643 - app_android.config_android - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp_android
2025-08-12 12:07:16,644 - app_android.config_android - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/files_to_push
2025-08-12 12:07:16,644 - __main__ - INFO - Using instance-specific database paths with suffix: _port_8081
2025-08-12 12:07:16,644 - __main__ - INFO - Using custom ports (Flask: 8081, Appium: 4724, WDA: 8300) - preserving existing processes for multi-instance support
2025-08-12 12:07:16,644 - __main__ - INFO - Skipping process termination when using custom ports for multi-instance support
