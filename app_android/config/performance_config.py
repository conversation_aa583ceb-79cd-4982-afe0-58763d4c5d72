# Performance optimization configuration for Android test execution
# Implements recommendations from ANDROID_PERFORMANCE_RECOMMENDATIONS.md

import time
import threading
from typing import Dict, Any, Optional

class PerformanceConfig:
    """
    Configuration class for Android test execution performance optimizations
    """
    
    def __init__(self):
        # Screenshot optimization settings (Step 1)
        self.screenshot_cache_enabled = True
        self.screenshot_cache_ttl = 2.0  # Cache screenshots for 2 seconds
        self.screenshot_frequency_limit = 1.5  # Minimum 1.5 seconds between screenshots
        self.screenshot_skip_duplicates = True
        self.screenshot_compression_enabled = True
        self.screenshot_compression_quality = 85  # JPEG quality for compression
        
        # Session management optimization settings (Step 3)
        self.health_check_interval = 10.0  # Increase from default 5s to 10s
        self.health_check_timeout = 3.0  # Reduce timeout from 5s to 3s
        self.session_refresh_threshold = 5  # Reduce from 7 to 5 failures
        self.session_validation_frequency = 30.0  # Check session every 30s instead of every action
        self.suspend_health_checks_during_actions = True
        
        # Performance monitoring
        self.enable_performance_metrics = True
        self.log_performance_warnings = True
        self.performance_warning_threshold = 2.0  # Warn if operations take > 2s
        
        # Cache and state management
        self._screenshot_cache = {}
        self._last_screenshot_time = 0
        self._last_health_check_time = 0
        self._cache_lock = threading.Lock()
        
    def should_take_screenshot(self, action_id: str = None) -> bool:
        """
        Determine if a screenshot should be taken based on frequency limits
        
        Args:
            action_id: Optional action ID to check for duplicates
            
        Returns:
            bool: True if screenshot should be taken
        """
        current_time = time.time()
        
        # Check frequency limit
        if current_time - self._last_screenshot_time < self.screenshot_frequency_limit:
            return False
            
        # Check for duplicate action_id in cache
        if action_id and self.screenshot_skip_duplicates:
            with self._cache_lock:
                if action_id in self._screenshot_cache:
                    cache_entry = self._screenshot_cache[action_id]
                    if current_time - cache_entry['timestamp'] < self.screenshot_cache_ttl:
                        return False
        
        return True
    
    def should_perform_health_check(self) -> bool:
        """
        Determine if a health check should be performed based on interval
        
        Returns:
            bool: True if health check should be performed
        """
        current_time = time.time()
        return current_time - self._last_health_check_time >= self.health_check_interval
    
    def update_screenshot_cache(self, action_id: str, screenshot_path: str):
        """
        Update the screenshot cache with new entry
        
        Args:
            action_id: Action ID for the screenshot
            screenshot_path: Path to the screenshot file
        """
        if not action_id or not self.screenshot_cache_enabled:
            return
            
        current_time = time.time()
        with self._cache_lock:
            self._screenshot_cache[action_id] = {
                'path': screenshot_path,
                'timestamp': current_time
            }
            self._last_screenshot_time = current_time
            
            # Clean old cache entries
            self._clean_cache(current_time)
    
    def get_cached_screenshot(self, action_id: str) -> Optional[str]:
        """
        Get cached screenshot path if available and valid
        
        Args:
            action_id: Action ID to look up
            
        Returns:
            str or None: Screenshot path if cached and valid
        """
        if not action_id or not self.screenshot_cache_enabled:
            return None
            
        current_time = time.time()
        with self._cache_lock:
            if action_id in self._screenshot_cache:
                cache_entry = self._screenshot_cache[action_id]
                if current_time - cache_entry['timestamp'] < self.screenshot_cache_ttl:
                    return cache_entry['path']
                else:
                    # Remove expired entry
                    del self._screenshot_cache[action_id]
        
        return None
    
    def update_health_check_time(self):
        """
        Update the last health check time
        """
        self._last_health_check_time = time.time()
    
    def _clean_cache(self, current_time: float):
        """
        Clean expired entries from the screenshot cache
        
        Args:
            current_time: Current timestamp
        """
        expired_keys = []
        for key, entry in self._screenshot_cache.items():
            if current_time - entry['timestamp'] > self.screenshot_cache_ttl:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self._screenshot_cache[key]
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        Get current performance statistics
        
        Returns:
            dict: Performance statistics
        """
        with self._cache_lock:
            return {
                'screenshot_cache_size': len(self._screenshot_cache),
                'last_screenshot_time': self._last_screenshot_time,
                'last_health_check_time': self._last_health_check_time,
                'screenshot_frequency_limit': self.screenshot_frequency_limit,
                'health_check_interval': self.health_check_interval,
                'cache_enabled': self.screenshot_cache_enabled
            }

# Global performance configuration instance
performance_config = PerformanceConfig()