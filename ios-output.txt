2025-08-12 09:47:29,510 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-08-12 09:47:29,511 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-08-12 09:47:29,512 - config - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/ios_data/test_cases
2025-08-12 09:47:29,512 - config - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports_ios
2025-08-12 09:47:29,512 - config - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots_ios
2025-08-12 09:47:29,513 - config - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/ios_data/reference_images
2025-08-12 09:47:29,513 - config - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/ios_data/test_suites
2025-08-12 09:47:29,514 - config - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports_ios/suites
2025-08-12 09:47:29,514 - config - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/recordings_ios
2025-08-12 09:47:29,514 - config - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp_ios
2025-08-12 09:47:29,515 - config - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/files_to_push
2025-08-12 09:47:29,515 - __main__ - INFO - Using default ports - killing existing processes to avoid conflicts
2025-08-12 09:47:29,515 - __main__ - INFO - Killing any existing Appium and iproxy processes...
2025-08-12 09:47:31,591 - __main__ - INFO - Existing processes terminated
