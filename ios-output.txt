2025-08-12 12:06:38,111 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-08-12 12:06:38,112 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-08-12 12:06:38,112 - config - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/ios_data/test_cases
2025-08-12 12:06:38,113 - config - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports_ios
2025-08-12 12:06:38,114 - config - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots_ios
2025-08-12 12:06:38,114 - config - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/ios_data/reference_images
2025-08-12 12:06:38,115 - config - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/ios_data/test_suites
2025-08-12 12:06:38,115 - config - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports_ios/suites
2025-08-12 12:06:38,116 - config - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/recordings_ios
2025-08-12 12:06:38,116 - config - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp_ios
2025-08-12 12:06:38,116 - config - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/files_to_push
2025-08-12 12:06:38,117 - __main__ - INFO - Using default ports - killing existing processes to avoid conflicts
2025-08-12 12:06:38,117 - __main__ - INFO - Killing any existing Appium and iproxy processes...
2025-08-12 12:06:40,208 - __main__ - INFO - Existing processes terminated
2025-08-12 12:06:41,256 - utils.global_values_db - INFO - Using global values database at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/data/global_values.db
2025-08-12 12:06:41,257 - utils.global_values_db - INFO - Global values database initialized successfully
2025-08-12 12:06:41,257 - utils.global_values_db - INFO - Using global values from config.py
2025-08-12 12:06:41,257 - utils.global_values_db - INFO - Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 2, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300, 'Connection Retry Attempts': 3, 'Connection Retry Delay': 2}
2025-08-12 12:06:41,259 - utils.healenium_config - INFO - Loaded Healenium configuration: enabled=True
2025-08-12 12:06:41,261 - appium_device_controller - WARNING - TouchAction not available in this Appium Python Client version - using W3C Actions fallback
2025-08-12 12:06:41,295 - AppiumDeviceController - INFO - Successfully imported Airtest library.
2025-08-12 12:06:41,622 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-08-12 12:06:41,689 - utils.database - INFO - === UPDATING TEST_STEPS TABLE SCHEMA ===
2025-08-12 12:06:41,690 - utils.database - INFO - Test_steps table schema updated successfully
2025-08-12 12:06:41,690 - utils.database - INFO - === UPDATING SCREENSHOTS TABLE SCHEMA ===
2025-08-12 12:06:41,690 - utils.database - INFO - Screenshots table schema updated successfully
2025-08-12 12:06:41,691 - utils.database - INFO - === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
2025-08-12 12:06:41,691 - utils.database - INFO - step_idx column already exists in execution_tracking table
2025-08-12 12:06:41,691 - utils.database - INFO - action_type column already exists in execution_tracking table
2025-08-12 12:06:41,691 - utils.database - INFO - action_params column already exists in execution_tracking table
2025-08-12 12:06:41,691 - utils.database - INFO - action_id column already exists in execution_tracking table
2025-08-12 12:06:41,691 - utils.database - INFO - Successfully updated execution_tracking table schema
2025-08-12 12:06:41,691 - utils.database - INFO - Database initialized successfully
2025-08-12 12:06:41,691 - utils.database - INFO - Checking initial database state...
2025-08-12 12:06:41,692 - utils.database - INFO - Database state: 0 suites, 0 cases, 0 steps, 0 screenshots, 0 tracking entries
2025-08-12 12:06:41,704 - app - INFO - Using directories from config.py:
2025-08-12 12:06:41,704 - app - INFO -   - TEST_CASES_DIR: /Users/<USER>/Documents/automation-tool/ios_data/test_cases
2025-08-12 12:06:41,704 - app - INFO -   - REFERENCE_IMAGES_DIR: /Users/<USER>/Documents/automation-tool/ios_data/reference_images
2025-08-12 12:06:41,704 - app - INFO -   - SCREENSHOTS_DIR: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots_ios
[2025-08-12 12:06:41,780] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8200
[2025-08-12 12:06:41,791] WARNING in appium_device_controller: Appium server check failed: HTTPConnectionPool(host='127.0.0.1', port=4723): Max retries exceeded with url: /wd/hub/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x11aa4af90>: Failed to establish a new connection: [Errno 61] Connection refused'))
[2025-08-12 12:06:41,791] INFO in appium_device_controller: Checking for existing Appium and iproxy processes...
[2025-08-12 12:06:41,826] INFO in appium_device_controller: Attempted to kill Appium processes
[2025-08-12 12:06:41,862] INFO in appium_device_controller: Attempted to kill iproxy processes (default ports only)
[2025-08-12 12:06:43,872] INFO in appium_device_controller: No Appium server detected. Starting a new one...
[2025-08-12 12:06:43,872] INFO in appium_device_controller: Using local Appium installation at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium
[2025-08-12 12:06:44,661] INFO in appium_device_controller: Installed Appium drivers: 
[2025-08-12 12:06:44,661] INFO in appium_device_controller: Installing XCUITest driver...
[31mError: ✖ A driver named "xcuitest" is already installed. Did you mean to update? Run "appium driver update". See installed drivers with "appium driver list --installed".[39m
[2025-08-12 12:06:45,503] ERROR in appium_device_controller: Error checking/installing drivers: Command '['/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium', 'driver', 'install', 'xcuitest']' returned non-zero exit status 1.
[2025-08-12 12:06:45,504] INFO in appium_device_controller: Enabling inspector plugin if available
[2025-08-12 12:06:45,504] INFO in appium_device_controller: Appium server output will be logged to: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/appium_server.log
[2025-08-12 12:06:45,511] INFO in appium_device_controller: Started Appium server using command: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium --base-path /wd/hub --port 4723 --relaxed-security --use-drivers xcuitest,uiautomator2 --use-plugins=inspector --session-override --allow-cors --allow-insecure chromedriver_autodownload
[2025-08-12 12:06:47,532] INFO in appium_device_controller: Appium server started successfully
[2025-08-12 12:06:47,532] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'bca0816f1bc2b01b9df7a451d958ebee99d6fcc2', 'built': '2025-08-11 19:15:57 +1000'}}}
[2025-08-12 12:06:49,539] INFO in appium_device_controller: Appium server started successfully
[2025-08-12 12:06:49,539] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'bca0816f1bc2b01b9df7a451d958ebee99d6fcc2', 'built': '2025-08-11 19:15:57 +1000'}}}
[2025-08-12 12:06:51,548] INFO in appium_device_controller: Appium server started successfully
[2025-08-12 12:06:51,549] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'bca0816f1bc2b01b9df7a451d958ebee99d6fcc2', 'built': '2025-08-11 19:15:57 +1000'}}}
[2025-08-12 12:06:53,553] INFO in appium_device_controller: Appium server started successfully
[2025-08-12 12:06:53,553] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'bca0816f1bc2b01b9df7a451d958ebee99d6fcc2', 'built': '2025-08-11 19:15:57 +1000'}}}
[2025-08-12 12:06:55,564] INFO in appium_device_controller: Appium server started successfully
[2025-08-12 12:06:55,564] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'bca0816f1bc2b01b9df7a451d958ebee99d6fcc2', 'built': '2025-08-11 19:15:57 +1000'}}}
[2025-08-12 12:06:57,570] INFO in appium_device_controller: Appium server started successfully
[2025-08-12 12:06:57,570] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'bca0816f1bc2b01b9df7a451d958ebee99d6fcc2', 'built': '2025-08-11 19:15:57 +1000'}}}
[2025-08-12 12:06:59,581] INFO in appium_device_controller: Appium server started successfully
[2025-08-12 12:06:59,581] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'bca0816f1bc2b01b9df7a451d958ebee99d6fcc2', 'built': '2025-08-11 19:15:57 +1000'}}}
[2025-08-12 12:07:01,592] INFO in appium_device_controller: Appium server started successfully
[2025-08-12 12:07:01,592] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'bca0816f1bc2b01b9df7a451d958ebee99d6fcc2', 'built': '2025-08-11 19:15:57 +1000'}}}
[2025-08-12 12:07:03,600] INFO in appium_device_controller: Appium server started successfully
[2025-08-12 12:07:03,600] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'bca0816f1bc2b01b9df7a451d958ebee99d6fcc2', 'built': '2025-08-11 19:15:57 +1000'}}}
[2025-08-12 12:07:05,611] INFO in appium_device_controller: Appium server started successfully
[2025-08-12 12:07:05,611] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'bca0816f1bc2b01b9df7a451d958ebee99d6fcc2', 'built': '2025-08-11 19:15:57 +1000'}}}
[2025-08-12 12:07:07,616] INFO in appium_device_controller: Appium server started successfully
[2025-08-12 12:07:07,617] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'bca0816f1bc2b01b9df7a451d958ebee99d6fcc2', 'built': '2025-08-11 19:15:57 +1000'}}}
[2025-08-12 12:07:09,624] INFO in appium_device_controller: Appium server started successfully
[2025-08-12 12:07:09,625] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'bca0816f1bc2b01b9df7a451d958ebee99d6fcc2', 'built': '2025-08-11 19:15:57 +1000'}}}
[2025-08-12 12:07:11,631] INFO in appium_device_controller: Appium server started successfully
[2025-08-12 12:07:11,632] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'bca0816f1bc2b01b9df7a451d958ebee99d6fcc2', 'built': '2025-08-11 19:15:57 +1000'}}}
[2025-08-12 12:07:13,637] INFO in appium_device_controller: Appium server started successfully
[2025-08-12 12:07:13,637] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'bca0816f1bc2b01b9df7a451d958ebee99d6fcc2', 'built': '2025-08-11 19:15:57 +1000'}}}
[2025-08-12 12:07:15,647] INFO in appium_device_controller: Appium server started successfully
[2025-08-12 12:07:15,647] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'bca0816f1bc2b01b9df7a451d958ebee99d6fcc2', 'built': '2025-08-11 19:15:57 +1000'}}}
Starting Mobile App Automation Tool...
Configuration:
  - Flask server port: 8080
  - Appium server port: 4723
  - WebDriverAgent port: 8200
Open your web browser and navigate to: http://localhost:8080
 * Serving Flask app 'app'
 * Debug mode: on
[2025-08-12 12:07:15,721] INFO in _internal: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8080
 * Running on http://**************:8080
[2025-08-12 12:07:15,722] INFO in _internal: [33mPress CTRL+C to quit[0m
[2025-08-12 12:07:19,897] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:19] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-08-12 12:07:24,898] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:24] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-08-12 12:07:25,372] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "GET / HTTP/1.1" 200 -
[2025-08-12 12:07:25,396] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
[2025-08-12 12:07:25,396] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "[36mGET /static/css/modern-styles.css HTTP/1.1[0m" 304 -
[2025-08-12 12:07:25,402] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "[36mGET /static/css/test-cases-styles.css HTTP/1.1[0m" 304 -
[2025-08-12 12:07:25,404] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "[36mGET /static/css/test-case.css HTTP/1.1[0m" 304 -
[2025-08-12 12:07:25,406] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "[36mGET /static/css/test-suites-styles.css HTTP/1.1[0m" 304 -
[2025-08-12 12:07:25,413] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "[36mGET /static/css/fixed-device-screen.css HTTP/1.1[0m" 304 -
[2025-08-12 12:07:25,416] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "[36mGET /static/css/execution-overlay.css HTTP/1.1[0m" 304 -
[2025-08-12 12:07:25,420] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "[36mGET /static/css/test-case-modification.css HTTP/1.1[0m" 304 -
[2025-08-12 12:07:25,422] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "[36mGET /static/css/detachable-device-screen.css HTTP/1.1[0m" 304 -
[2025-08-12 12:07:25,423] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "[36mGET /static/css/actionStyles.css HTTP/1.1[0m" 304 -
[2025-08-12 12:07:25,428] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "[36mGET /static/css/bulk-locator-manager.css HTTP/1.1[0m" 304 -
[2025-08-12 12:07:25,433] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "[36mGET /static/js/modules/uiUtils.js HTTP/1.1[0m" 304 -
[2025-08-12 12:07:25,433] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "[36mGET /static/js/modules/actionFormManager.js HTTP/1.1[0m" 304 -
[2025-08-12 12:07:25,438] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "[36mGET /static/js/modules/reportAndFormUtils.js HTTP/1.1[0m" 304 -
[2025-08-12 12:07:25,441] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "[36mGET /static/js/export-run.js HTTP/1.1[0m" 304 -
[2025-08-12 12:07:25,444] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
[2025-08-12 12:07:25,450] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "[36mGET /static/img/no_device.png HTTP/1.1[0m" 304 -
[2025-08-12 12:07:25,456] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "[36mGET /static/js/bulk-locator-manager.js HTTP/1.1[0m" 304 -
[2025-08-12 12:07:25,458] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "[36mGET /static/js/utils.js HTTP/1.1[0m" 304 -
[2025-08-12 12:07:25,459] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "GET /static/js/action-manager.js?v=1754967445 HTTP/1.1" 200 -
[2025-08-12 12:07:25,465] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "[36mGET /static/js/modules/ElementInteractions.js HTTP/1.1[0m" 304 -
[2025-08-12 12:07:25,467] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "[36mGET /static/js/fixed-device-screen.js HTTP/1.1[0m" 304 -
[2025-08-12 12:07:25,471] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "[36mGET /static/js/detachable-device-screen.js HTTP/1.1[0m" 304 -
[2025-08-12 12:07:25,472] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "[36mGET /static/js/execution-overlay.js HTTP/1.1[0m" 304 -
[2025-08-12 12:07:25,473] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "[36mGET /static/js/execution-manager.js HTTP/1.1[0m" 304 -
[2025-08-12 12:07:25,478] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "[36mGET /static/js/modules/TestCaseManager.js HTTP/1.1[0m" 304 -
[2025-08-12 12:07:25,483] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "[36mGET /static/js/action-description.js HTTP/1.1[0m" 304 -
[2025-08-12 12:07:25,488] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "[36mGET /static/js/multi-step-action.js HTTP/1.1[0m" 304 -
[2025-08-12 12:07:25,492] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "[36mGET /static/js/repeat-steps-action.js HTTP/1.1[0m" 304 -
[2025-08-12 12:07:25,497] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "[36mGET /static/js/hook-action.js HTTP/1.1[0m" 304 -
[2025-08-12 12:07:25,504] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "[36mGET /static/js/random-data-generator.js HTTP/1.1[0m" 304 -
[2025-08-12 12:07:25,505] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "[36mGET /static/js/modules/tap-fallback-manager.js HTTP/1.1[0m" 304 -
[2025-08-12 12:07:25,507] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "[36mGET /static/js/modules/fallback-locators.js HTTP/1.1[0m" 304 -
[2025-08-12 12:07:25,508] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "GET /static/js/main.js?v=1754967445 HTTP/1.1" 200 -
[2025-08-12 12:07:25,516] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "[36mGET /static/js/test_suites.js HTTP/1.1[0m" 304 -
[2025-08-12 12:07:25,523] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "[36mGET /static/js/settings.js HTTP/1.1[0m" 304 -
[2025-08-12 12:07:25,529] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "[36mGET /static/js/environment-variables.js HTTP/1.1[0m" 304 -
[2025-08-12 12:07:25,538] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "[36mGET /static/js/test-case-modification.js HTTP/1.1[0m" 304 -
[2025-08-12 12:07:25,539] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "[36mGET /static/js/import-export.js HTTP/1.1[0m" 304 -
[2025-08-12 12:07:25,544] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "[36mGET /static/guide/faq_guide.html HTTP/1.1[0m" 304 -
[2025-08-12 12:07:25,697] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "GET /api/environments HTTP/1.1" 200 -
[2025-08-12 12:07:25,698] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "GET /api/random_data/generators HTTP/1.1" 200 -
[2025-08-12 12:07:25,717] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "GET /api/reference_images HTTP/1.1" 200 -
[2025-08-12 12:07:25,733] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-08-12 12:07:25,752] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "GET /api/bulk_locator/backups HTTP/1.1" 200 -
[2025-08-12 12:07:25,757] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "GET /api/settings HTTP/1.1" 200 -
[2025-08-12 12:07:25,760] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "GET /api/environment_variables HTTP/1.1" 200 -
[2025-08-12 12:07:25,767] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "GET /api/random_data/generators HTTP/1.1" 200 -
[2025-08-12 12:07:25,780] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "GET /api/reference_images HTTP/1.1" 200 -
[2025-08-12 12:07:25,791] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "GET /api/test_suites/list HTTP/1.1" 200 -
[2025-08-12 12:07:25,794] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "[33mGET /api/environments/current HTTP/1.1[0m" 404 -
[2025-08-12 12:07:25,807] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "[36mGET /static/img/favicon.ico HTTP/1.1[0m" 304 -
[2025-08-12 12:07:25,824] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "GET /api/reference_images HTTP/1.1" 200 -
[2025-08-12 12:07:25,841] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "GET /api/reference_images HTTP/1.1" 200 -
[2025-08-12 12:07:25,858] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "GET /api/environments/7/variables HTTP/1.1" 200 -
[2025-08-12 12:07:25,863] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "GET /api/reference_images HTTP/1.1" 200 -
[2025-08-12 12:07:25,880] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "GET /api/reference_images HTTP/1.1" 200 -
[2025-08-12 12:07:25,934] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "GET /api/test_cases/action_types HTTP/1.1" 200 -
[2025-08-12 12:07:25,993] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:25] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-08-12 12:07:26,015] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:26] "GET /api/recording/list HTTP/1.1" 200 -
[2025-08-12 12:07:26,040] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:26] "GET /api/test_cases/locator_types HTTP/1.1" 200 -
[2025-08-12 12:07:26,049] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:26] "GET /api/tools/scan-databases HTTP/1.1" 200 -
[2025-08-12 12:07:26,087] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:26] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-08-12 12:07:27,441] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8200
[2025-08-12 12:07:27,447] INFO in appium_device_controller: Appium server is running and ready
[2025-08-12 12:07:27,447] INFO in appium_device_controller: Appium server is already running and responsive
[2025-08-12 12:07:27,448] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:27] "GET /api/devices HTTP/1.1" 200 -
[2025-08-12 12:07:28,941] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8200
[2025-08-12 12:07:28,947] INFO in appium_device_controller: Appium server is running and ready
[2025-08-12 12:07:28,947] INFO in appium_device_controller: Appium server is already running and responsive
[2025-08-12 12:07:28,948] INFO in appium_device_controller: Connecting to device: ********-00186C801E13C01E with options: None, platform hint: iOS
[2025-08-12 12:07:30,686] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:30] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-08-12 12:07:30,690] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:30] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-08-12 12:07:31,967] INFO in grid_manager: Falling back to direct connection for iOS
[2025-08-12 12:07:31,969] WARNING in grid_config: Grid not available, falling back to direct connection for ios
[2025-08-12 12:07:31,969] INFO in grid_manager: Session created: session_1754964451_iOS for iOS
[2025-08-12 12:07:31,971] ERROR in appium_device_controller: Failed to get session info from AppiumDeviceManager
[2025-08-12 12:07:31,971] INFO in appium_device_controller: AppiumDeviceManager connection failed, falling back to direct connection
[2025-08-12 12:07:31,972] INFO in appium_device_controller: Connection attempt 1/3
[2025-08-12 12:07:31,972] INFO in appium_device_controller: Using provided platform hint: iOS
[2025-08-12 12:07:31,972] INFO in appium_device_controller: Using custom WebDriverAgent URL: http://localhost:8200
[2025-08-12 12:07:31,972] INFO in appium_device_controller: Desired capabilities: {'platformName': 'iOS', 'deviceName': '********-00186C801E13C01E', 'udid': '********-00186C801E13C01E', 'newCommandTimeout': 300, 'noReset': True, 'automationName': 'XCUITest', 'xcodeOrgId': '', 'xcodeSigningId': 'iPhone Developer', 'webDriverAgentUrl': 'http://localhost:8200', 'showIOSLog': True}
[2025-08-12 12:07:31,972] INFO in appium_device_controller: Connecting to Appium server with options: {'platformName': 'iOS', 'appium:deviceName': '********-00186C801E13C01E', 'appium:udid': '********-00186C801E13C01E', 'appium:newCommandTimeout': 300, 'appium:noReset': True, 'appium:automationName': 'XCUITest', 'appium:xcodeOrgId': '', 'appium:xcodeSigningId': 'iPhone Developer', 'appium:webDriverAgentUrl': 'http://localhost:8200', 'appium:showIOSLog': True}
[2025-08-12 12:07:31,972] INFO in appium_device_controller: Connecting to iOS device via WebDriverAgent
[2025-08-12 12:07:31,994] INFO in appium_device_controller: Device ********-00186C801E13C01E is listed and trusted
[2025-08-12 12:07:31,994] WARNING in appium_device_controller: Error reading WDA port mapping: cannot access local variable 'os' where it is not associated with a value, using default port 8200
[2025-08-12 12:07:31,994] INFO in appium_device_controller: Using WebDriverAgent URL: http://localhost:8200 for device ********-00186C801E13C01E
[2025-08-12 12:07:35,685] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:35] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-08-12 12:07:35,689] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:35] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-08-12 12:07:40,683] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:40] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-08-12 12:07:40,687] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:40] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-08-12 12:07:41,997] INFO in appium_device_controller: WebDriverAgent not detected at http://localhost:8200, will try to start port forwarding: HTTPConnectionPool(host='localhost', port=8200): Read timed out. (read timeout=10)
[2025-08-12 12:07:41,998] INFO in appium_device_controller: Port 8200 is already in use, checking if WebDriverAgent is responding...
[2025-08-12 12:07:45,683] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:45] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-08-12 12:07:45,688] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:45] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-08-12 12:07:47,002] WARNING in appium_device_controller: Port 8200 is in use but WebDriverAgent is not responding: HTTPConnectionPool(host='localhost', port=8200): Read timed out. (read timeout=5). Will restart.
[2025-08-12 12:07:47,164] INFO in appium_device_controller: Killed process 80738 using port 8200
[2025-08-12 12:07:48,296] INFO in appium_device_controller: Using tidevice for port forwarding: 8200 -> 8100
[2025-08-12 12:07:50,310] INFO in appium_device_controller: tidevice port forwarding started successfully
[2025-08-12 12:07:50,310] INFO in appium_device_controller: Checking WebDriverAgent connection (attempt 1/5)...
[2025-08-12 12:07:50,321] INFO in appium_device_controller: WebDriverAgent is running at http://localhost:8200
[2025-08-12 12:07:50,321] INFO in appium_device_controller: WebDriverAgent status: {'value': {'build': {'version': '9.5.0', 'time': 'Jun  8 2025 18:35:21', 'productBundleIdentifier': 'com.facebook.WebDriverAgentRunner'}, 'os': {'testmanagerdVersion': 65535, 'name': 'iOS', 'sdkVersion': '18.4', 'version': '18.5'}, 'device': 'iphone', 'ios': {'ip': '**************'}, 'message': 'WebDriverAgent is ready to accept commands', 'state': 'success', 'ready': True}, 'sessionId': 'F0CC463C-F453-4B8F-8352-34B737EF7993'}
[2025-08-12 12:07:50,325] INFO in appium_device_controller: Appium server is already running
[2025-08-12 12:07:50,325] INFO in appium_device_controller: iOS connection attempt 1/3
[2025-08-12 12:07:50,325] INFO in appium_device_controller: Using capabilities: {'platformName': 'iOS', 'appium:deviceName': '********-00186C801E13C01E', 'appium:udid': '********-00186C801E13C01E', 'appium:newCommandTimeout': 300, 'appium:noReset': True, 'appium:automationName': 'XCUITest', 'appium:xcodeOrgId': '', 'appium:xcodeSigningId': 'iPhone Developer', 'appium:webDriverAgentUrl': 'http://localhost:8200', 'appium:showIOSLog': True, 'webDriverAgentUrl': 'http://localhost:8200'}
[2025-08-12 12:07:50,329] INFO in appium_device_controller: Appium server status before connection: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'bca0816f1bc2b01b9df7a451d958ebee99d6fcc2', 'built': '2025-08-11 19:15:57 +1000'}}}
[2025-08-12 12:07:50,331] WARNING in grid_config: Grid not available, falling back to direct connection for ios
[2025-08-12 12:07:50,331] INFO in appium_device_controller: Using connection URL: http://127.0.0.1:4723/wd/hub
[2025-08-12 12:07:50,331] INFO in appium_device_controller: Connecting to Appium server at http://127.0.0.1:4723/wd/hub
[2025-08-12 12:07:50,685] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:50] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-08-12 12:07:50,689] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:50] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-08-12 12:07:50,905] INFO in appium_device_controller: Wrapping driver with Healenium self-healing capabilities
[2025-08-12 12:07:50,908] INFO in appium_device_controller: Driver successfully wrapped with Healenium
[2025-08-12 12:07:50,908] INFO in appium_device_controller: Successfully connected to iOS device
[2025-08-12 12:07:50,908] INFO in appium_device_controller: Connected with session ID: 5f8d2829-2eb4-4b24-b9e3-89eabd4a567b
[2025-08-12 12:07:50,908] INFO in appium_device_controller: Connection verified with capabilities: iOS
[2025-08-12 12:07:50,909] INFO in appium_device_controller: Initializing platform helpers for iOS
[2025-08-12 12:07:50,909] INFO in appium_device_controller: Getting device dimensions
[2025-08-12 12:07:51,798] INFO in appium_device_controller: Got device dimensions from Appium: 393x852
[2025-08-12 12:07:51,798] INFO in appium_device_controller: Device dimensions: (393, 852)
[2025-08-12 12:07:51,800] WARNING in appium_device_controller: Failed to initialize ImageMatcher: No module named 'app.utils'; 'app' is not a package
[2025-08-12 12:07:51,800] INFO in appium_device_controller: Initializing iOS-specific helpers
[2025-08-12 12:07:51,800] INFO in appium_device_controller: Setting up iOS predicate string and class chain support
[2025-08-12 12:07:51,800] INFO in appium_device_controller: iOS version: 18.0
[2025-08-12 12:07:51,800] INFO in appium_device_controller: Using modern keyboard handling for iOS 15+
[2025-08-12 12:07:51,800] INFO in appium_device_controller: Platform helpers initialization completed
[2025-08-12 12:07:51,800] INFO in appium_device_controller: Successfully connected to device on attempt 1
[2025-08-12 12:07:51,801] INFO in action_factory: Registered basic actions: tap, wait
[2025-08-12 12:07:51,801] INFO in action_factory: Special case: Registering tap_if_image_exists_action.py as 'tapIfImageExists'
[2025-08-12 12:07:51,801] INFO in action_factory: Registered action handler for 'tapIfImageExists'
[2025-08-12 12:07:51,802] INFO in action_factory: Registered action handler for 'multiStep'
[2025-08-12 12:07:51,802] INFO in action_factory: Special case: Registering cleanup_steps_action.py as 'cleanupSteps'
[2025-08-12 12:07:51,802] INFO in action_factory: Registered action handler for 'cleanupSteps'
[2025-08-12 12:07:51,802] INFO in action_factory: Registered action handler for 'swipe'
[2025-08-12 12:07:51,803] INFO in action_factory: Registered action handler for 'getParam'
[2025-08-12 12:07:51,803] INFO in action_factory: Registered action handler for 'wait'
[2025-08-12 12:07:51,803] INFO in action_factory: Registered action handler for 'terminateApp'
[2025-08-12 12:07:51,803] INFO in action_factory: Registered action handler for 'doubleClickImage'
[2025-08-12 12:07:51,804] INFO in action_factory: Registered action handler for 'uninstallApp'
[2025-08-12 12:07:51,804] INFO in action_factory: Registered action handler for 'text'
[2025-08-12 12:07:51,806] ERROR in action_factory: Error loading action handler from tap_if_text_exists_action: expected 'except' or 'finally' block (tap_if_text_exists_action.py, line 170)
[2025-08-12 12:07:51,807] INFO in action_factory: Registered action handler for 'waitTill'
[2025-08-12 12:07:51,807] INFO in action_factory: Registered action handler for 'hookAction'
[2025-08-12 12:07:51,807] INFO in action_factory: Registered action handler for 'inputText'
[2025-08-12 12:07:51,808] INFO in global_values_db: Using global values database at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/data/global_values.db
[2025-08-12 12:07:51,808] INFO in global_values_db: Global values database initialized successfully
[2025-08-12 12:07:51,809] INFO in global_values_db: Using global values from config.py
[2025-08-12 12:07:51,809] INFO in global_values_db: Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 2, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300, 'Connection Retry Attempts': 3, 'Connection Retry Delay': 2}
[2025-08-12 12:07:51,811] INFO in action_factory: Registered action handler for 'setParam'
[2025-08-12 12:07:51,811] INFO in action_factory: Special case: Registering repeat_steps_action.py as 'repeatSteps'
[2025-08-12 12:07:51,811] INFO in action_factory: Registered action handler for 'repeatSteps'
[2025-08-12 12:07:51,811] INFO in action_factory: Registered action handler for 'iosFunctions'
[2025-08-12 12:07:51,812] INFO in action_factory: Registered action handler for 'swipeTillVisible'
[2025-08-12 12:07:51,812] INFO in action_factory: Registered action handler for 'clickImage'
[2025-08-12 12:07:51,812] INFO in action_factory: Registered action handler for 'tap'
[2025-08-12 12:07:51,813] INFO in action_factory: Special case: Registering take_screenshot_action.py as 'takeScreenshot'
[2025-08-12 12:07:51,813] INFO in action_factory: Registered action handler for 'takeScreenshot'
[2025-08-12 12:07:51,813] INFO in action_factory: Special case: Registering tap_if_locator_exists_action.py as 'tapIfLocatorExists'
[2025-08-12 12:07:51,813] INFO in action_factory: Registered action handler for 'tapIfLocatorExists'
[2025-08-12 12:07:51,814] INFO in action_factory: Registered action handler for 'tapAndType'
[2025-08-12 12:07:51,814] INFO in action_factory: Special case: Registering tap_on_text_action.py as 'tapOnText'
[2025-08-12 12:07:51,814] INFO in action_factory: Registered action handler for 'tapOnText'
[2025-08-12 12:07:51,814] INFO in action_factory: Registered action handler for 'launchApp'
[2025-08-12 12:07:51,815] INFO in action_factory: Registered action handler for 'ifThenSteps'
[2025-08-12 12:07:51,815] INFO in action_factory: Special case: Registering info_action.py as 'info'
[2025-08-12 12:07:51,815] INFO in action_factory: Registered action handler for 'info'
[2025-08-12 12:07:51,815] INFO in action_factory: Registered action handler for 'waitElement'
[2025-08-12 12:07:51,816] INFO in action_factory: Registered action handler for 'compareValue'
[2025-08-12 12:07:51,816] INFO in action_factory: Registered action handler for 'deviceBack'
[2025-08-12 12:07:51,816] INFO in action_factory: Special case: Registering check_if_exists_action.py as 'exists'
[2025-08-12 12:07:51,817] INFO in action_factory: Registered action handler for 'exists'
[2025-08-12 12:07:51,817] INFO in action_factory: Registered action handler for 'clickElement'
[2025-08-12 12:07:51,817] INFO in action_factory: Registered action handler for 'randomData'
[2025-08-12 12:07:51,818] INFO in action_factory: Registered action handler for 'getValue'
[2025-08-12 12:07:51,818] INFO in action_factory: Registered action handler for 'test'
[2025-08-12 12:07:51,818] INFO in action_factory: Registered action handler for 'restartApp'
[2025-08-12 12:07:51,819] INFO in action_factory: Special case: Registering double_tap_action.py as 'doubleTap'
[2025-08-12 12:07:51,819] INFO in action_factory: Registered action handler for 'doubleTap'
[2025-08-12 12:07:51,819] INFO in action_factory: Registered action types: ['tap', 'wait', 'tapIfImageExists', 'multiStep', 'cleanupSteps', 'swipe', 'getParam', 'terminateApp', 'doubleClickImage', 'uninstallApp', 'text', 'waitTill', 'hookAction', 'inputText', 'setParam', 'repeatSteps', 'iosFunctions', 'swipeTillVisible', 'clickImage', 'takeScreenshot', 'tapIfLocatorExists', 'tapAndType', 'tapOnText', 'launchApp', 'ifThenSteps', 'info', 'waitElement', 'compareValue', 'deviceBack', 'exists', 'clickElement', 'randomData', 'getValue', 'test', 'restartApp', 'doubleTap']
[2025-08-12 12:07:51,819] INFO in action_factory: Handler for 'tap': TapAction
[2025-08-12 12:07:51,819] INFO in action_factory: Handler for 'wait': WaitAction
[2025-08-12 12:07:51,819] INFO in action_factory: Handler for 'tapIfImageExists': TapIfImageExistsAction
[2025-08-12 12:07:51,819] INFO in action_factory: Handler for 'multiStep': MultiStepAction
[2025-08-12 12:07:51,820] INFO in action_factory: Handler for 'cleanupSteps': CleanupStepsAction
[2025-08-12 12:07:51,820] INFO in action_factory: Handler for 'swipe': SwipeAction
[2025-08-12 12:07:51,820] INFO in action_factory: Handler for 'getParam': GetParamAction
[2025-08-12 12:07:51,820] INFO in action_factory: Handler for 'terminateApp': TerminateAppAction
[2025-08-12 12:07:51,820] INFO in action_factory: Handler for 'doubleClickImage': DoubleClickImageAction
[2025-08-12 12:07:51,820] INFO in action_factory: Handler for 'uninstallApp': UninstallAppAction
[2025-08-12 12:07:51,820] INFO in action_factory: Handler for 'text': TextAction
[2025-08-12 12:07:51,820] INFO in action_factory: Handler for 'waitTill': WaitTillAction
[2025-08-12 12:07:51,820] INFO in action_factory: Handler for 'hookAction': HookAction
[2025-08-12 12:07:51,820] INFO in action_factory: Handler for 'inputText': InputTextAction
[2025-08-12 12:07:51,820] INFO in action_factory: Handler for 'setParam': SetParamAction
[2025-08-12 12:07:51,820] INFO in action_factory: Handler for 'repeatSteps': RepeatStepsAction
[2025-08-12 12:07:51,820] INFO in action_factory: Handler for 'iosFunctions': IosFunctionsAction
[2025-08-12 12:07:51,820] INFO in action_factory: Handler for 'swipeTillVisible': SwipeTillVisibleAction
[2025-08-12 12:07:51,820] INFO in action_factory: Handler for 'clickImage': ClickImageAction
[2025-08-12 12:07:51,820] INFO in action_factory: Handler for 'takeScreenshot': TakeScreenshotAction
[2025-08-12 12:07:51,820] INFO in action_factory: Handler for 'tapIfLocatorExists': TapIfLocatorExistsAction
[2025-08-12 12:07:51,820] INFO in action_factory: Handler for 'tapAndType': TapAndTypeAction
[2025-08-12 12:07:51,820] INFO in action_factory: Handler for 'tapOnText': TapOnTextAction
[2025-08-12 12:07:51,820] INFO in action_factory: Handler for 'launchApp': LaunchAppAction
[2025-08-12 12:07:51,820] INFO in action_factory: Handler for 'ifThenSteps': IfThenStepsAction
[2025-08-12 12:07:51,820] INFO in action_factory: Handler for 'info': InfoAction
[2025-08-12 12:07:51,820] INFO in action_factory: Handler for 'waitElement': WaitElementAction
[2025-08-12 12:07:51,820] INFO in action_factory: Handler for 'compareValue': CompareValueAction
[2025-08-12 12:07:51,820] INFO in action_factory: Handler for 'deviceBack': DeviceBackAction
[2025-08-12 12:07:51,820] INFO in action_factory: Handler for 'exists': CheckIfExistsAction
[2025-08-12 12:07:51,820] INFO in action_factory: Handler for 'clickElement': ClickElementAction
[2025-08-12 12:07:51,820] INFO in action_factory: Handler for 'randomData': RandomDataAction
[2025-08-12 12:07:51,821] INFO in action_factory: Handler for 'getValue': GetValueAction
[2025-08-12 12:07:51,821] INFO in action_factory: Handler for 'test': TestAction
[2025-08-12 12:07:51,821] INFO in action_factory: Handler for 'restartApp': RestartAppAction
[2025-08-12 12:07:51,821] INFO in action_factory: Handler for 'doubleTap': DoubleTapAction
[2025-08-12 12:07:51,821] INFO in appium_device_controller: Initializing Airtest connection for device: ********-00186C801E13C01E...
[2025-08-12 12:07:51,821] INFO in appium_device_controller: Connecting to iOS device with WebDriverAgent at http://localhost:8200
[2025-08-12 12:07:51,827] INFO in ios_device: Initialized MinimalIOSDevice for ********-00186C801E13C01E with WDA at http://localhost:8200
[2025-08-12 12:07:51,831] ERROR in appium_device_controller: Failed to get screen resolution from iOS device
[2025-08-12 12:07:51,831] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-08-12 12:07:51,831] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp_ios/screenshots/placeholder.png (save_debug=False)
[2025-08-12 12:07:51,831] INFO in appium_device_controller: Taking screenshot using native Appium driver (attempt 1/3)
[2025-08-12 12:07:52,063] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-08-12 12:07:52,901] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:52] "POST /api/device/connect HTTP/1.1" 200 -
[2025-08-12 12:07:53,925] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-08-12 12:07:53,925] INFO in appium_device_controller: Taking screenshot using native Appium driver (attempt 1/3)
[2025-08-12 12:07:54,224] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-08-12 12:07:54,225] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:54] "GET /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1754964445684_6erufjb5r_1754956091087_t6hgnhoyw&t=1754964473922 HTTP/1.1" 200 -
[2025-08-12 12:07:55,683] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:55] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-08-12 12:07:55,687] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:55] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-08-12 12:07:59,022] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:59] "GET /api/recording/list HTTP/1.1" 200 -
[2025-08-12 12:07:59,084] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:07:59] "GET /api/recording/list HTTP/1.1" 200 -
[2025-08-12 12:08:00,683] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:00] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-08-12 12:08:00,687] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:00] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-08-12 12:08:01,780] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:01] "GET /api/test_cases/load/Postcode_Flow_AU_ANDROID_20250702070922.json HTTP/1.1" 200 -
[2025-08-12 12:08:01,796] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:01] "POST /api/screenshots/delete_all HTTP/1.1" 200 -
[2025-08-12 12:08:05,684] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:05] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-08-12 12:08:05,689] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:05] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-08-12 12:08:06,636] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:06] "GET /api/recording/list HTTP/1.1" 200 -
[2025-08-12 12:08:06,682] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:06] "GET /api/recording/list HTTP/1.1" 200 -
[2025-08-12 12:08:10,686] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:10] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-08-12 12:08:10,691] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:10] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-08-12 12:08:11,842] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:11] "GET /api/test_cases/load/App_Settings_AU_20250609145542.json HTTP/1.1" 200 -
[2025-08-12 12:08:11,857] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:11] "POST /api/screenshots/delete_all HTTP/1.1" 200 -
[2025-08-12 12:08:15,685] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:15] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-08-12 12:08:15,690] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:15] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-08-12 12:08:18,910] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:18] "POST /api/environments/current HTTP/1.1" 200 -
[2025-08-12 12:08:20,684] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:20] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-08-12 12:08:20,689] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:20] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-08-12 12:08:22,316] DEBUG in appium_device_controller: Session ID: 5f8d2829-2eb4-4b24-b9e3-89eabd4a567b
[2025-08-12 12:08:22,317] DEBUG in appium_device_controller: Session is responsive (session status check passed)
[2025-08-12 12:08:22,317] INFO in player: Executing action: {'action_id': 'XEbZHdi0GT', 'executionTime': '3369ms', 'package_id': 'au.com.kmart', 'timestamp': 1749445093180, 'type': 'restartApp'}
[2025-08-12 12:08:22,317] INFO in player: DEBUG: Current test index at start of execute_action: 0
[2025-08-12 12:08:22,317] INFO in player: DEBUG: Player instance ID: **********, has current_test_idx: True
[2025-08-12 12:08:22,317] INFO in player: DEBUG: Player's current_test_idx value: 0
[2025-08-12 12:08:22,317] INFO in player: DEBUG: Using action_id from action: XEbZHdi0GT
[2025-08-12 12:08:22,317] INFO in player: DEBUG: Before tracking - current_test_idx.value: 0
[2025-08-12 12:08:22,317] INFO in player: DEBUG: Using local_test_idx: 0 for tracking
[2025-08-12 12:08:22,317] INFO in player: ========== PLAYER EXECUTING ACTION WITH TEST_IDX: 0 ==========
[2025-08-12 12:08:22,317] INFO in player: ========== ACTION TYPE: restartApp ==========
[2025-08-12 12:08:22,317] INFO in player: ========== ACTION ID: XEbZHdi0GT ==========
[2025-08-12 12:08:22,317] INFO in player: ========== GLOBAL CURRENT_TEST_IDX: 0 ==========
[2025-08-12 12:08:22,317] INFO in player: ========== PLAYER CURRENT_TEST_IDX: 0 ==========
[2025-08-12 12:08:22,325] INFO in player: Tracked execution in database: test_idx=0, step_idx=0, action_type=restartApp, action_id=XEbZHdi0GT
[2025-08-12 12:08:22,325] INFO in player: Skipping device connection verification for better performance
[2025-08-12 12:08:22,326] INFO in action_factory: Registered basic actions: tap, wait
[2025-08-12 12:08:22,326] INFO in action_factory: Special case: Registering tap_if_image_exists_action.py as 'tapIfImageExists'
[2025-08-12 12:08:22,326] INFO in action_factory: Registered action handler for 'tapIfImageExists'
[2025-08-12 12:08:22,326] INFO in action_factory: Registered action handler for 'multiStep'
[2025-08-12 12:08:22,326] INFO in action_factory: Special case: Registering cleanup_steps_action.py as 'cleanupSteps'
[2025-08-12 12:08:22,326] INFO in action_factory: Registered action handler for 'cleanupSteps'
[2025-08-12 12:08:22,326] INFO in action_factory: Registered action handler for 'swipe'
[2025-08-12 12:08:22,327] INFO in action_factory: Registered action handler for 'getParam'
[2025-08-12 12:08:22,328] INFO in action_factory: Registered action handler for 'wait'
[2025-08-12 12:08:22,328] INFO in action_factory: Registered action handler for 'terminateApp'
[2025-08-12 12:08:22,328] INFO in action_factory: Registered action handler for 'doubleClickImage'
[2025-08-12 12:08:22,328] INFO in action_factory: Registered action handler for 'uninstallApp'
[2025-08-12 12:08:22,329] INFO in action_factory: Registered action handler for 'text'
[2025-08-12 12:08:22,333] ERROR in action_factory: Error loading action handler from tap_if_text_exists_action: expected 'except' or 'finally' block (tap_if_text_exists_action.py, line 170)
[2025-08-12 12:08:22,333] INFO in action_factory: Registered action handler for 'waitTill'
[2025-08-12 12:08:22,333] INFO in action_factory: Registered action handler for 'hookAction'
[2025-08-12 12:08:22,333] INFO in action_factory: Registered action handler for 'inputText'
[2025-08-12 12:08:22,334] INFO in action_factory: Registered action handler for 'setParam'
[2025-08-12 12:08:22,334] INFO in action_factory: Special case: Registering repeat_steps_action.py as 'repeatSteps'
[2025-08-12 12:08:22,334] INFO in action_factory: Registered action handler for 'repeatSteps'
[2025-08-12 12:08:22,334] INFO in action_factory: Registered action handler for 'iosFunctions'
[2025-08-12 12:08:22,334] INFO in action_factory: Registered action handler for 'swipeTillVisible'
[2025-08-12 12:08:22,334] INFO in action_factory: Registered action handler for 'clickImage'
[2025-08-12 12:08:22,334] INFO in action_factory: Registered action handler for 'tap'
[2025-08-12 12:08:22,334] INFO in action_factory: Special case: Registering take_screenshot_action.py as 'takeScreenshot'
[2025-08-12 12:08:22,334] INFO in action_factory: Registered action handler for 'takeScreenshot'
[2025-08-12 12:08:22,335] INFO in action_factory: Special case: Registering tap_if_locator_exists_action.py as 'tapIfLocatorExists'
[2025-08-12 12:08:22,335] INFO in action_factory: Registered action handler for 'tapIfLocatorExists'
[2025-08-12 12:08:22,335] INFO in action_factory: Registered action handler for 'tapAndType'
[2025-08-12 12:08:22,335] INFO in action_factory: Special case: Registering tap_on_text_action.py as 'tapOnText'
[2025-08-12 12:08:22,335] INFO in action_factory: Registered action handler for 'tapOnText'
[2025-08-12 12:08:22,335] INFO in action_factory: Registered action handler for 'launchApp'
[2025-08-12 12:08:22,335] INFO in action_factory: Registered action handler for 'ifThenSteps'
[2025-08-12 12:08:22,335] INFO in action_factory: Special case: Registering info_action.py as 'info'
[2025-08-12 12:08:22,335] INFO in action_factory: Registered action handler for 'info'
[2025-08-12 12:08:22,335] INFO in action_factory: Registered action handler for 'waitElement'
[2025-08-12 12:08:22,336] INFO in action_factory: Registered action handler for 'compareValue'
[2025-08-12 12:08:22,336] INFO in action_factory: Registered action handler for 'deviceBack'
[2025-08-12 12:08:22,336] INFO in action_factory: Special case: Registering check_if_exists_action.py as 'exists'
[2025-08-12 12:08:22,336] INFO in action_factory: Registered action handler for 'exists'
[2025-08-12 12:08:22,336] INFO in action_factory: Registered action handler for 'clickElement'
[2025-08-12 12:08:22,336] INFO in action_factory: Registered action handler for 'randomData'
[2025-08-12 12:08:22,336] INFO in action_factory: Registered action handler for 'getValue'
[2025-08-12 12:08:22,336] INFO in action_factory: Registered action handler for 'test'
[2025-08-12 12:08:22,336] INFO in action_factory: Registered action handler for 'restartApp'
[2025-08-12 12:08:22,336] INFO in action_factory: Special case: Registering double_tap_action.py as 'doubleTap'
[2025-08-12 12:08:22,336] INFO in action_factory: Registered action handler for 'doubleTap'
[2025-08-12 12:08:22,336] INFO in action_factory: Registered action types: ['tap', 'wait', 'tapIfImageExists', 'multiStep', 'cleanupSteps', 'swipe', 'getParam', 'terminateApp', 'doubleClickImage', 'uninstallApp', 'text', 'waitTill', 'hookAction', 'inputText', 'setParam', 'repeatSteps', 'iosFunctions', 'swipeTillVisible', 'clickImage', 'takeScreenshot', 'tapIfLocatorExists', 'tapAndType', 'tapOnText', 'launchApp', 'ifThenSteps', 'info', 'waitElement', 'compareValue', 'deviceBack', 'exists', 'clickElement', 'randomData', 'getValue', 'test', 'restartApp', 'doubleTap']
[2025-08-12 12:08:22,336] INFO in action_factory: Handler for 'tap': TapAction
[2025-08-12 12:08:22,336] INFO in action_factory: Handler for 'wait': WaitAction
[2025-08-12 12:08:22,336] INFO in action_factory: Handler for 'tapIfImageExists': TapIfImageExistsAction
[2025-08-12 12:08:22,337] INFO in action_factory: Handler for 'multiStep': MultiStepAction
[2025-08-12 12:08:22,337] INFO in action_factory: Handler for 'cleanupSteps': CleanupStepsAction
[2025-08-12 12:08:22,337] INFO in action_factory: Handler for 'swipe': SwipeAction
[2025-08-12 12:08:22,337] INFO in action_factory: Handler for 'getParam': GetParamAction
[2025-08-12 12:08:22,337] INFO in action_factory: Handler for 'terminateApp': TerminateAppAction
[2025-08-12 12:08:22,337] INFO in action_factory: Handler for 'doubleClickImage': DoubleClickImageAction
[2025-08-12 12:08:22,337] INFO in action_factory: Handler for 'uninstallApp': UninstallAppAction
[2025-08-12 12:08:22,337] INFO in action_factory: Handler for 'text': TextAction
[2025-08-12 12:08:22,338] INFO in action_factory: Handler for 'waitTill': WaitTillAction
[2025-08-12 12:08:22,338] INFO in action_factory: Handler for 'hookAction': HookAction
[2025-08-12 12:08:22,338] INFO in action_factory: Handler for 'inputText': InputTextAction
[2025-08-12 12:08:22,338] INFO in action_factory: Handler for 'setParam': SetParamAction
[2025-08-12 12:08:22,338] INFO in action_factory: Handler for 'repeatSteps': RepeatStepsAction
[2025-08-12 12:08:22,338] INFO in action_factory: Handler for 'iosFunctions': IosFunctionsAction
[2025-08-12 12:08:22,338] INFO in action_factory: Handler for 'swipeTillVisible': SwipeTillVisibleAction
[2025-08-12 12:08:22,338] INFO in action_factory: Handler for 'clickImage': ClickImageAction
[2025-08-12 12:08:22,338] INFO in action_factory: Handler for 'takeScreenshot': TakeScreenshotAction
[2025-08-12 12:08:22,339] INFO in action_factory: Handler for 'tapIfLocatorExists': TapIfLocatorExistsAction
[2025-08-12 12:08:22,339] INFO in action_factory: Handler for 'tapAndType': TapAndTypeAction
[2025-08-12 12:08:22,339] INFO in action_factory: Handler for 'tapOnText': TapOnTextAction
[2025-08-12 12:08:22,339] INFO in action_factory: Handler for 'launchApp': LaunchAppAction
[2025-08-12 12:08:22,339] INFO in action_factory: Handler for 'ifThenSteps': IfThenStepsAction
[2025-08-12 12:08:22,339] INFO in action_factory: Handler for 'info': InfoAction
[2025-08-12 12:08:22,339] INFO in action_factory: Handler for 'waitElement': WaitElementAction
[2025-08-12 12:08:22,339] INFO in action_factory: Handler for 'compareValue': CompareValueAction
[2025-08-12 12:08:22,339] INFO in action_factory: Handler for 'deviceBack': DeviceBackAction
[2025-08-12 12:08:22,340] INFO in action_factory: Handler for 'exists': CheckIfExistsAction
[2025-08-12 12:08:22,340] INFO in action_factory: Handler for 'clickElement': ClickElementAction
[2025-08-12 12:08:22,340] INFO in action_factory: Handler for 'randomData': RandomDataAction
[2025-08-12 12:08:22,340] INFO in action_factory: Handler for 'getValue': GetValueAction
[2025-08-12 12:08:22,340] INFO in action_factory: Handler for 'test': TestAction
[2025-08-12 12:08:22,340] INFO in action_factory: Handler for 'restartApp': RestartAppAction
[2025-08-12 12:08:22,340] INFO in action_factory: Handler for 'doubleTap': DoubleTapAction
[2025-08-12 12:08:22,340] INFO in player: Forwarding restartApp action to ActionFactory with params: {'package_id': 'au.com.kmart'}
[2025-08-12 12:08:22,340] INFO in action_factory: Requested action type: 'restartApp', Available types: ['cleanupSteps', 'clickElement', 'clickImage', 'compareValue', 'deviceBack', 'doubleClickImage', 'doubleTap', 'exists', 'getParam', 'getValue', 'hookAction', 'ifThenSteps', 'info', 'inputText', 'iosFunctions', 'launchApp', 'multiStep', 'randomData', 'repeatSteps', 'restartApp', 'setParam', 'swipe', 'swipeTillVisible', 'takeScreenshot', 'tap', 'tapAndType', 'tapIfImageExists', 'tapIfLocatorExists', 'tapOnText', 'terminateApp', 'test', 'text', 'uninstallApp', 'wait', 'waitElement', 'waitTill']
[2025-08-12 12:08:22,340] INFO in action_factory: Action parameters before env resolution: {'package_id': 'au.com.kmart'}
[2025-08-12 12:08:22,340] INFO in action_factory: Applying environment variable resolution for env ID 7 to action params
[2025-08-12 12:08:22,340] INFO in action_factory: Action parameters after env resolution: {'package_id': 'au.com.kmart'}
[2025-08-12 12:08:22,340] INFO in restart_app_action: [HOOK_DEBUG] RestartAppAction.execute called with params: {'package_id': 'au.com.kmart'}
[2025-08-12 12:08:22,340] INFO in restart_app_action: Restarting app: au.com.kmart
[2025-08-12 12:08:22,340] INFO in appium_device_controller: Terminating app: au.com.kmart
[2025-08-12 12:08:22,340] INFO in appium_device_controller: Using XCUITest to terminate iOS app: au.com.kmart
[2025-08-12 12:08:22,924] DEBUG in appium_device_controller: Session ID: 5f8d2829-2eb4-4b24-b9e3-89eabd4a567b
[2025-08-12 12:08:22,924] DEBUG in appium_device_controller: Session is responsive (session status check passed)
[2025-08-12 12:08:22,925] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:22] "GET /api/session/health HTTP/1.1" 200 -
[2025-08-12 12:08:23,403] INFO in appium_device_controller: Launching app: au.com.kmart
[2025-08-12 12:08:23,403] INFO in appium_device_controller: Using XCUITest to launch iOS app: au.com.kmart
[2025-08-12 12:08:24,558] INFO in player: Skipping delay after action execution for better performance
[2025-08-12 12:08:24,558] WARNING in player: Could not take screenshot after action: cannot access local variable 'i' where it is not associated with a value
[2025-08-12 12:08:24,559] INFO in player: DEBUG: Using local_test_idx: 0 for tracking completion
[2025-08-12 12:08:24,559] INFO in player: DEBUG: Player instance ID: **********, has current_test_idx: True
[2025-08-12 12:08:24,559] INFO in player: DEBUG: Player's current_test_idx value: 0
[2025-08-12 12:08:24,559] INFO in player: DEBUG: Global current_test_idx.value: 0
[2025-08-12 12:08:24,559] INFO in player: ========== PLAYER COMPLETING ACTION WITH TEST_IDX: 0 ==========
[2025-08-12 12:08:24,559] INFO in player: ========== ACTION TYPE: restartApp ==========
[2025-08-12 12:08:24,559] INFO in player: ========== GLOBAL CURRENT_TEST_IDX: 0 ==========
[2025-08-12 12:08:24,559] INFO in player: ========== PLAYER CURRENT_TEST_IDX: 0 ==========
[2025-08-12 12:08:24,562] INFO in player: Tracked execution completion in database: test_idx=0, step_idx=0, action_type=restartApp, status=passed
[2025-08-12 12:08:25,562] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-08-12 12:08:25,562] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp_ios/screenshots/placeholder.png (save_debug=False)
[2025-08-12 12:08:25,562] INFO in appium_device_controller: Taking screenshot using native Appium driver (attempt 1/3)
[2025-08-12 12:08:25,684] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:25] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-08-12 12:08:25,689] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:25] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-08-12 12:08:25,840] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-08-12 12:08:25,845] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:25] "POST /api/action/execute HTTP/1.1" 200 -
[2025-08-12 12:08:25,851] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-08-12 12:08:25,852] INFO in appium_device_controller: Taking screenshot using native Appium driver (attempt 1/3)
[2025-08-12 12:08:26,124] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports_ios/suite_execution_abc3a7ac-9fee-4890-988b-339d850ff1e2/screenshots/latest.png
[2025-08-12 12:08:26,124] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-08-12 12:08:26,124] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:26] "GET /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1754964445684_6erufjb5r_1754956091087_t6hgnhoyw&t=1754964505847 HTTP/1.1" 200 -
[2025-08-12 12:08:30,684] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:30] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-08-12 12:08:37,233] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:37] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-08-12 12:08:39,630] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:39] "GET /api/test_suites/list HTTP/1.1" 200 -
[2025-08-12 12:08:39,639] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:39] "GET /api/test_suites/list HTTP/1.1" 200 -
[2025-08-12 12:08:40,685] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:40] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-08-12 12:08:42,258] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:42] "POST /api/screenshots/delete_all HTTP/1.1" 200 -
[2025-08-12 12:08:42,260] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:42] "GET /api/test_suites/90853884-1b79-4f05-8542-f590d5d307a1 HTTP/1.1" 200 -
[2025-08-12 12:08:42,277] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:42] "GET /api/test_cases/load/Delivery__CNC_20250505163250.json HTTP/1.1" 200 -
[2025-08-12 12:08:42,310] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:42] "GET /api/test_cases/load/All_Sign_ins_20250501131834.json HTTP/1.1" 200 -
[2025-08-12 12:08:42,319] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:42] "GET /api/test_cases/load/WishList_20250510110236.json HTTP/1.1" 200 -
[2025-08-12 12:08:42,326] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:42] "GET /api/test_cases/load/KmartProdSignin_20250426221008.json HTTP/1.1" 200 -
[2025-08-12 12:08:42,338] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:42] "GET /api/test_cases/load/AU_MyAccount_20250506181929.json HTTP/1.1" 200 -
[2025-08-12 12:08:42,344] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:42] "GET /api/test_cases/load/Others_20250512190312.json HTTP/1.1" 200 -
[2025-08-12 12:08:42,354] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:42] "GET /api/test_cases/load/Postcode_Flow_20250502104451.json HTTP/1.1" 200 -
[2025-08-12 12:08:42,362] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:42] "GET /api/test_cases/load/App_Settings_AU_20250609145542.json HTTP/1.1" 200 -
[2025-08-12 12:08:42,375] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:42] "GET /api/test_cases/load/temp_20250615085036.json HTTP/1.1" 200 -
[2025-08-12 12:08:42,386] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:42] "GET /api/test_cases/load/All_Payments_Check_20250512194232.json HTTP/1.1" 200 -
[2025-08-12 12:08:42,394] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:42] "GET /api/test_cases/load/Browse__PDP_20250510095542.json HTTP/1.1" 200 -
[2025-08-12 12:08:45,687] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:45] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-08-12 12:08:48,900] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:48] "POST /api/report/initialize HTTP/1.1" 200 -
[2025-08-12 12:08:48,912] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:48] "POST /api/screenshots/delete_all HTTP/1.1" 200 -
[2025-08-12 12:08:48,933] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:48] "POST /api/database/clear_screenshots HTTP/1.1" 200 -
[2025-08-12 12:08:48,977] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-08-12 12:08:48,977] INFO in appium_device_controller: Taking screenshot using native Appium driver (attempt 1/3)
[2025-08-12 12:08:49,026] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:49] "GET /api/get_execution_context HTTP/1.1" 200 -
[2025-08-12 12:08:49,047] DEBUG in appium_device_controller: Session ID: 5f8d2829-2eb4-4b24-b9e3-89eabd4a567b
[2025-08-12 12:08:49,048] DEBUG in appium_device_controller: Session is responsive (session status check passed)
[2025-08-12 12:08:49,048] INFO in player: Executing action: {'action_id': 'HotUJOd6oB', 'executionTime': '2464ms', 'package_id': 'au.com.kmart', 'timestamp': 1746598746211, 'type': 'restartApp', 'test_idx': 0}
[2025-08-12 12:08:49,048] INFO in player: DEBUG: Current test index at start of execute_action: 0
[2025-08-12 12:08:49,048] INFO in player: DEBUG: Player instance ID: **********, has current_test_idx: True
[2025-08-12 12:08:49,048] INFO in player: DEBUG: Player's current_test_idx value: 0
[2025-08-12 12:08:49,048] INFO in player: DEBUG: Using action_id from action: HotUJOd6oB
[2025-08-12 12:08:49,049] INFO in player: DEBUG: Before tracking - current_test_idx.value: 0
[2025-08-12 12:08:49,049] INFO in player: DEBUG: Using test_idx from action: 0 for tracking
[2025-08-12 12:08:49,049] INFO in player: DEBUG: Updated player's current_test_idx to match action: 0
[2025-08-12 12:08:49,049] INFO in player: DEBUG: Updated global current_test_idx.value to match action: 0
[2025-08-12 12:08:49,049] INFO in player: ========== PLAYER EXECUTING ACTION WITH TEST_IDX: 0 ==========
[2025-08-12 12:08:49,049] INFO in player: ========== ACTION TYPE: restartApp ==========
[2025-08-12 12:08:49,050] INFO in player: ========== ACTION ID: HotUJOd6oB ==========
[2025-08-12 12:08:49,050] INFO in player: ========== GLOBAL CURRENT_TEST_IDX: 0 ==========
[2025-08-12 12:08:49,050] INFO in player: ========== PLAYER CURRENT_TEST_IDX: 0 ==========
[2025-08-12 12:08:49,052] INFO in player: Tracked execution in database: test_idx=0, step_idx=1, action_type=restartApp, action_id=HotUJOd6oB
[2025-08-12 12:08:49,052] INFO in player: Skipping device connection verification for better performance
[2025-08-12 12:08:49,053] INFO in action_factory: Registered basic actions: tap, wait
[2025-08-12 12:08:49,053] INFO in action_factory: Special case: Registering tap_if_image_exists_action.py as 'tapIfImageExists'
[2025-08-12 12:08:49,053] INFO in action_factory: Registered action handler for 'tapIfImageExists'
[2025-08-12 12:08:49,053] INFO in action_factory: Registered action handler for 'multiStep'
[2025-08-12 12:08:49,054] INFO in action_factory: Special case: Registering cleanup_steps_action.py as 'cleanupSteps'
[2025-08-12 12:08:49,054] INFO in action_factory: Registered action handler for 'cleanupSteps'
[2025-08-12 12:08:49,054] INFO in action_factory: Registered action handler for 'swipe'
[2025-08-12 12:08:49,054] INFO in action_factory: Registered action handler for 'getParam'
[2025-08-12 12:08:49,054] INFO in action_factory: Registered action handler for 'wait'
[2025-08-12 12:08:49,054] INFO in action_factory: Registered action handler for 'terminateApp'
[2025-08-12 12:08:49,054] INFO in action_factory: Registered action handler for 'doubleClickImage'
[2025-08-12 12:08:49,054] INFO in action_factory: Registered action handler for 'uninstallApp'
[2025-08-12 12:08:49,054] INFO in action_factory: Registered action handler for 'text'
[2025-08-12 12:08:49,058] ERROR in action_factory: Error loading action handler from tap_if_text_exists_action: expected 'except' or 'finally' block (tap_if_text_exists_action.py, line 170)
[2025-08-12 12:08:49,058] INFO in action_factory: Registered action handler for 'waitTill'
[2025-08-12 12:08:49,058] INFO in action_factory: Registered action handler for 'hookAction'
[2025-08-12 12:08:49,058] INFO in action_factory: Registered action handler for 'inputText'
[2025-08-12 12:08:49,058] INFO in action_factory: Registered action handler for 'setParam'
[2025-08-12 12:08:49,058] INFO in action_factory: Special case: Registering repeat_steps_action.py as 'repeatSteps'
[2025-08-12 12:08:49,058] INFO in action_factory: Registered action handler for 'repeatSteps'
[2025-08-12 12:08:49,058] INFO in action_factory: Registered action handler for 'iosFunctions'
[2025-08-12 12:08:49,059] INFO in action_factory: Registered action handler for 'swipeTillVisible'
[2025-08-12 12:08:49,059] INFO in action_factory: Registered action handler for 'clickImage'
[2025-08-12 12:08:49,059] INFO in action_factory: Registered action handler for 'tap'
[2025-08-12 12:08:49,059] INFO in action_factory: Special case: Registering take_screenshot_action.py as 'takeScreenshot'
[2025-08-12 12:08:49,059] INFO in action_factory: Registered action handler for 'takeScreenshot'
[2025-08-12 12:08:49,059] INFO in action_factory: Special case: Registering tap_if_locator_exists_action.py as 'tapIfLocatorExists'
[2025-08-12 12:08:49,059] INFO in action_factory: Registered action handler for 'tapIfLocatorExists'
[2025-08-12 12:08:49,059] INFO in action_factory: Registered action handler for 'tapAndType'
[2025-08-12 12:08:49,059] INFO in action_factory: Special case: Registering tap_on_text_action.py as 'tapOnText'
[2025-08-12 12:08:49,059] INFO in action_factory: Registered action handler for 'tapOnText'
[2025-08-12 12:08:49,059] INFO in action_factory: Registered action handler for 'launchApp'
[2025-08-12 12:08:49,060] INFO in action_factory: Registered action handler for 'ifThenSteps'
[2025-08-12 12:08:49,060] INFO in action_factory: Special case: Registering info_action.py as 'info'
[2025-08-12 12:08:49,060] INFO in action_factory: Registered action handler for 'info'
[2025-08-12 12:08:49,060] INFO in action_factory: Registered action handler for 'waitElement'
[2025-08-12 12:08:49,060] INFO in action_factory: Registered action handler for 'compareValue'
[2025-08-12 12:08:49,060] INFO in action_factory: Registered action handler for 'deviceBack'
[2025-08-12 12:08:49,060] INFO in action_factory: Special case: Registering check_if_exists_action.py as 'exists'
[2025-08-12 12:08:49,060] INFO in action_factory: Registered action handler for 'exists'
[2025-08-12 12:08:49,060] INFO in action_factory: Registered action handler for 'clickElement'
[2025-08-12 12:08:49,061] INFO in action_factory: Registered action handler for 'randomData'
[2025-08-12 12:08:49,061] INFO in action_factory: Registered action handler for 'getValue'
[2025-08-12 12:08:49,061] INFO in action_factory: Registered action handler for 'test'
[2025-08-12 12:08:49,061] INFO in action_factory: Registered action handler for 'restartApp'
[2025-08-12 12:08:49,061] INFO in action_factory: Special case: Registering double_tap_action.py as 'doubleTap'
[2025-08-12 12:08:49,061] INFO in action_factory: Registered action handler for 'doubleTap'
[2025-08-12 12:08:49,061] INFO in action_factory: Registered action types: ['tap', 'wait', 'tapIfImageExists', 'multiStep', 'cleanupSteps', 'swipe', 'getParam', 'terminateApp', 'doubleClickImage', 'uninstallApp', 'text', 'waitTill', 'hookAction', 'inputText', 'setParam', 'repeatSteps', 'iosFunctions', 'swipeTillVisible', 'clickImage', 'takeScreenshot', 'tapIfLocatorExists', 'tapAndType', 'tapOnText', 'launchApp', 'ifThenSteps', 'info', 'waitElement', 'compareValue', 'deviceBack', 'exists', 'clickElement', 'randomData', 'getValue', 'test', 'restartApp', 'doubleTap']
[2025-08-12 12:08:49,061] INFO in action_factory: Handler for 'tap': TapAction
[2025-08-12 12:08:49,061] INFO in action_factory: Handler for 'wait': WaitAction
[2025-08-12 12:08:49,061] INFO in action_factory: Handler for 'tapIfImageExists': TapIfImageExistsAction
[2025-08-12 12:08:49,061] INFO in action_factory: Handler for 'multiStep': MultiStepAction
[2025-08-12 12:08:49,061] INFO in action_factory: Handler for 'cleanupSteps': CleanupStepsAction
[2025-08-12 12:08:49,062] INFO in action_factory: Handler for 'swipe': SwipeAction
[2025-08-12 12:08:49,062] INFO in action_factory: Handler for 'getParam': GetParamAction
[2025-08-12 12:08:49,062] INFO in action_factory: Handler for 'terminateApp': TerminateAppAction
[2025-08-12 12:08:49,062] INFO in action_factory: Handler for 'doubleClickImage': DoubleClickImageAction
[2025-08-12 12:08:49,062] INFO in action_factory: Handler for 'uninstallApp': UninstallAppAction
[2025-08-12 12:08:49,062] INFO in action_factory: Handler for 'text': TextAction
[2025-08-12 12:08:49,062] INFO in action_factory: Handler for 'waitTill': WaitTillAction
[2025-08-12 12:08:49,062] INFO in action_factory: Handler for 'hookAction': HookAction
[2025-08-12 12:08:49,062] INFO in action_factory: Handler for 'inputText': InputTextAction
[2025-08-12 12:08:49,062] INFO in action_factory: Handler for 'setParam': SetParamAction
[2025-08-12 12:08:49,062] INFO in action_factory: Handler for 'repeatSteps': RepeatStepsAction
[2025-08-12 12:08:49,062] INFO in action_factory: Handler for 'iosFunctions': IosFunctionsAction
[2025-08-12 12:08:49,062] INFO in action_factory: Handler for 'swipeTillVisible': SwipeTillVisibleAction
[2025-08-12 12:08:49,062] INFO in action_factory: Handler for 'clickImage': ClickImageAction
[2025-08-12 12:08:49,062] INFO in action_factory: Handler for 'takeScreenshot': TakeScreenshotAction
[2025-08-12 12:08:49,062] INFO in action_factory: Handler for 'tapIfLocatorExists': TapIfLocatorExistsAction
[2025-08-12 12:08:49,062] INFO in action_factory: Handler for 'tapAndType': TapAndTypeAction
[2025-08-12 12:08:49,062] INFO in action_factory: Handler for 'tapOnText': TapOnTextAction
[2025-08-12 12:08:49,062] INFO in action_factory: Handler for 'launchApp': LaunchAppAction
[2025-08-12 12:08:49,062] INFO in action_factory: Handler for 'ifThenSteps': IfThenStepsAction
[2025-08-12 12:08:49,062] INFO in action_factory: Handler for 'info': InfoAction
[2025-08-12 12:08:49,062] INFO in action_factory: Handler for 'waitElement': WaitElementAction
[2025-08-12 12:08:49,062] INFO in action_factory: Handler for 'compareValue': CompareValueAction
[2025-08-12 12:08:49,063] INFO in action_factory: Handler for 'deviceBack': DeviceBackAction
[2025-08-12 12:08:49,063] INFO in action_factory: Handler for 'exists': CheckIfExistsAction
[2025-08-12 12:08:49,063] INFO in action_factory: Handler for 'clickElement': ClickElementAction
[2025-08-12 12:08:49,063] INFO in action_factory: Handler for 'randomData': RandomDataAction
[2025-08-12 12:08:49,063] INFO in action_factory: Handler for 'getValue': GetValueAction
[2025-08-12 12:08:49,063] INFO in action_factory: Handler for 'test': TestAction
[2025-08-12 12:08:49,063] INFO in action_factory: Handler for 'restartApp': RestartAppAction
[2025-08-12 12:08:49,063] INFO in action_factory: Handler for 'doubleTap': DoubleTapAction
[2025-08-12 12:08:49,063] INFO in player: Forwarding restartApp action to ActionFactory with params: {'package_id': 'au.com.kmart'}
[2025-08-12 12:08:49,063] INFO in action_factory: Requested action type: 'restartApp', Available types: ['cleanupSteps', 'clickElement', 'clickImage', 'compareValue', 'deviceBack', 'doubleClickImage', 'doubleTap', 'exists', 'getParam', 'getValue', 'hookAction', 'ifThenSteps', 'info', 'inputText', 'iosFunctions', 'launchApp', 'multiStep', 'randomData', 'repeatSteps', 'restartApp', 'setParam', 'swipe', 'swipeTillVisible', 'takeScreenshot', 'tap', 'tapAndType', 'tapIfImageExists', 'tapIfLocatorExists', 'tapOnText', 'terminateApp', 'test', 'text', 'uninstallApp', 'wait', 'waitElement', 'waitTill']
[2025-08-12 12:08:49,063] INFO in action_factory: Action parameters before env resolution: {'package_id': 'au.com.kmart'}
[2025-08-12 12:08:49,063] INFO in action_factory: Applying environment variable resolution for env ID 7 to action params
[2025-08-12 12:08:49,063] INFO in action_factory: Action parameters after env resolution: {'package_id': 'au.com.kmart'}
[2025-08-12 12:08:49,063] INFO in restart_app_action: [HOOK_DEBUG] RestartAppAction.execute called with params: {'package_id': 'au.com.kmart'}
[2025-08-12 12:08:49,063] INFO in restart_app_action: Restarting app: au.com.kmart
[2025-08-12 12:08:49,064] INFO in appium_device_controller: Terminating app: au.com.kmart
[2025-08-12 12:08:49,064] INFO in appium_device_controller: Using XCUITest to terminate iOS app: au.com.kmart
[2025-08-12 12:08:49,283] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports_ios/testsuite_execution_20250812_120848/screenshots/latest.png
[2025-08-12 12:08:49,283] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-08-12 12:08:49,285] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:49] "GET /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1754964445684_6erufjb5r_1754956091087_t6hgnhoyw&t=1754964505847 HTTP/1.1" 200 -
[2025-08-12 12:08:50,274] INFO in appium_device_controller: Launching app: au.com.kmart
[2025-08-12 12:08:50,274] INFO in appium_device_controller: Using XCUITest to launch iOS app: au.com.kmart
[2025-08-12 12:08:51,426] INFO in player: Skipping delay after action execution for better performance
[2025-08-12 12:08:51,426] WARNING in player: Could not take screenshot after action: cannot access local variable 'i' where it is not associated with a value
[2025-08-12 12:08:51,426] INFO in player: DEBUG: Using test_idx from action: 0 for tracking completion
[2025-08-12 12:08:51,426] INFO in player: DEBUG: Updated player's current_test_idx to match action: 0
[2025-08-12 12:08:51,426] INFO in player: DEBUG: Updated global current_test_idx.value to match action: 0
[2025-08-12 12:08:51,426] INFO in player: DEBUG: Player instance ID: **********, has current_test_idx: True
[2025-08-12 12:08:51,426] INFO in player: DEBUG: Player's current_test_idx value: 0
[2025-08-12 12:08:51,427] INFO in player: DEBUG: Global current_test_idx.value: 0
[2025-08-12 12:08:51,427] INFO in player: ========== PLAYER COMPLETING ACTION WITH TEST_IDX: 0 ==========
[2025-08-12 12:08:51,427] INFO in player: ========== ACTION TYPE: restartApp ==========
[2025-08-12 12:08:51,427] INFO in player: ========== GLOBAL CURRENT_TEST_IDX: 0 ==========
[2025-08-12 12:08:51,427] INFO in player: ========== PLAYER CURRENT_TEST_IDX: 0 ==========
[2025-08-12 12:08:51,428] INFO in player: Tracked execution completion in database: test_idx=0, step_idx=1, action_type=restartApp, status=passed
[2025-08-12 12:08:52,431] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports_ios/testsuite_execution_20250812_120848/screenshots
[2025-08-12 12:08:52,432] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports_ios/testsuite_execution_20250812_120848/screenshots/screenshot_20250812_120852.png (save_debug=False)
[2025-08-12 12:08:52,432] INFO in appium_device_controller: Taking screenshot using native Appium driver (attempt 1/3)
[2025-08-12 12:08:52,698] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports_ios/testsuite_execution_20250812_120848/screenshots/latest.png
[2025-08-12 12:08:52,698] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250812_120852.png
[2025-08-12 12:08:52,707] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:52] "POST /api/action/execute HTTP/1.1" 200 -
[2025-08-12 12:08:52,716] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-08-12 12:08:52,716] INFO in appium_device_controller: Taking screenshot using native Appium driver (attempt 1/3)
[2025-08-12 12:08:52,924] DEBUG in appium_device_controller: Session ID: 5f8d2829-2eb4-4b24-b9e3-89eabd4a567b
[2025-08-12 12:08:52,925] DEBUG in appium_device_controller: Session is responsive (session status check passed)
[2025-08-12 12:08:52,925] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:52] "GET /api/session/health HTTP/1.1" 200 -
[2025-08-12 12:08:53,022] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports_ios/testsuite_execution_20250812_120848/screenshots/latest.png
[2025-08-12 12:08:53,022] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-08-12 12:08:53,022] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:53] "GET /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1754964445684_6erufjb5r_1754956091087_t6hgnhoyw&t=************* HTTP/1.1" 200 -
[2025-08-12 12:08:53,219] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:53] "GET /api/get_execution_context HTTP/1.1" 200 -
[2025-08-12 12:08:53,229] DEBUG in appium_device_controller: Session ID: 5f8d2829-2eb4-4b24-b9e3-89eabd4a567b
[2025-08-12 12:08:53,230] DEBUG in appium_device_controller: Session is responsive (session status check passed)
[2025-08-12 12:08:53,231] INFO in player: Executing action: {'action_id': 'rkL0oz4kiL', 'executionTime': '6388ms', 'interval': 0.5, 'locator_type': 'accessibility_id', 'locator_value': 'txtHomeAccountCtaSignIn', 'method': 'locator', 'timeout': 10, 'timestamp': *************, 'type': 'tap', 'test_idx': 0}
[2025-08-12 12:08:53,231] INFO in player: DEBUG: Current test index at start of execute_action: 0
[2025-08-12 12:08:53,231] INFO in player: DEBUG: Player instance ID: **********, has current_test_idx: True
[2025-08-12 12:08:53,231] INFO in player: DEBUG: Player's current_test_idx value: 0
[2025-08-12 12:08:53,231] INFO in player: DEBUG: Using action_id from action: rkL0oz4kiL
[2025-08-12 12:08:53,231] INFO in player: DEBUG: Before tracking - current_test_idx.value: 0
[2025-08-12 12:08:53,231] INFO in player: DEBUG: Using test_idx from action: 0 for tracking
[2025-08-12 12:08:53,231] INFO in player: DEBUG: Updated player's current_test_idx to match action: 0
[2025-08-12 12:08:53,232] INFO in player: DEBUG: Updated global current_test_idx.value to match action: 0
[2025-08-12 12:08:53,232] INFO in player: ========== PLAYER EXECUTING ACTION WITH TEST_IDX: 0 ==========
[2025-08-12 12:08:53,232] INFO in player: ========== ACTION TYPE: tap ==========
[2025-08-12 12:08:53,232] INFO in player: ========== ACTION ID: rkL0oz4kiL ==========
[2025-08-12 12:08:53,232] INFO in player: ========== GLOBAL CURRENT_TEST_IDX: 0 ==========
[2025-08-12 12:08:53,232] INFO in player: ========== PLAYER CURRENT_TEST_IDX: 0 ==========
[2025-08-12 12:08:53,234] INFO in player: Tracked execution in database: test_idx=0, step_idx=2, action_type=tap, action_id=rkL0oz4kiL
[2025-08-12 12:08:53,234] INFO in player: Skipping device connection verification for better performance
[2025-08-12 12:08:53,234] INFO in player: Tapping on element with accessibility_id: txtHomeAccountCtaSignIn, timeout=10s
[2025-08-12 12:08:53,234] INFO in action_factory: Registered basic actions: tap, wait
[2025-08-12 12:08:53,234] INFO in action_factory: Special case: Registering tap_if_image_exists_action.py as 'tapIfImageExists'
[2025-08-12 12:08:53,234] INFO in action_factory: Registered action handler for 'tapIfImageExists'
[2025-08-12 12:08:53,234] INFO in action_factory: Registered action handler for 'multiStep'
[2025-08-12 12:08:53,235] INFO in action_factory: Special case: Registering cleanup_steps_action.py as 'cleanupSteps'
[2025-08-12 12:08:53,235] INFO in action_factory: Registered action handler for 'cleanupSteps'
[2025-08-12 12:08:53,235] INFO in action_factory: Registered action handler for 'swipe'
[2025-08-12 12:08:53,235] INFO in action_factory: Registered action handler for 'getParam'
[2025-08-12 12:08:53,235] INFO in action_factory: Registered action handler for 'wait'
[2025-08-12 12:08:53,236] INFO in action_factory: Registered action handler for 'terminateApp'
[2025-08-12 12:08:53,236] INFO in action_factory: Registered action handler for 'doubleClickImage'
[2025-08-12 12:08:53,236] INFO in action_factory: Registered action handler for 'uninstallApp'
[2025-08-12 12:08:53,236] INFO in action_factory: Registered action handler for 'text'
[2025-08-12 12:08:53,239] ERROR in action_factory: Error loading action handler from tap_if_text_exists_action: expected 'except' or 'finally' block (tap_if_text_exists_action.py, line 170)
[2025-08-12 12:08:53,239] INFO in action_factory: Registered action handler for 'waitTill'
[2025-08-12 12:08:53,239] INFO in action_factory: Registered action handler for 'hookAction'
[2025-08-12 12:08:53,239] INFO in action_factory: Registered action handler for 'inputText'
[2025-08-12 12:08:53,239] INFO in action_factory: Registered action handler for 'setParam'
[2025-08-12 12:08:53,239] INFO in action_factory: Special case: Registering repeat_steps_action.py as 'repeatSteps'
[2025-08-12 12:08:53,239] INFO in action_factory: Registered action handler for 'repeatSteps'
[2025-08-12 12:08:53,240] INFO in action_factory: Registered action handler for 'iosFunctions'
[2025-08-12 12:08:53,240] INFO in action_factory: Registered action handler for 'swipeTillVisible'
[2025-08-12 12:08:53,240] INFO in action_factory: Registered action handler for 'clickImage'
[2025-08-12 12:08:53,240] INFO in action_factory: Registered action handler for 'tap'
[2025-08-12 12:08:53,240] INFO in action_factory: Special case: Registering take_screenshot_action.py as 'takeScreenshot'
[2025-08-12 12:08:53,240] INFO in action_factory: Registered action handler for 'takeScreenshot'
[2025-08-12 12:08:53,240] INFO in action_factory: Special case: Registering tap_if_locator_exists_action.py as 'tapIfLocatorExists'
[2025-08-12 12:08:53,240] INFO in action_factory: Registered action handler for 'tapIfLocatorExists'
[2025-08-12 12:08:53,240] INFO in action_factory: Registered action handler for 'tapAndType'
[2025-08-12 12:08:53,240] INFO in action_factory: Special case: Registering tap_on_text_action.py as 'tapOnText'
[2025-08-12 12:08:53,240] INFO in action_factory: Registered action handler for 'tapOnText'
[2025-08-12 12:08:53,241] INFO in action_factory: Registered action handler for 'launchApp'
[2025-08-12 12:08:53,241] INFO in action_factory: Registered action handler for 'ifThenSteps'
[2025-08-12 12:08:53,241] INFO in action_factory: Special case: Registering info_action.py as 'info'
[2025-08-12 12:08:53,241] INFO in action_factory: Registered action handler for 'info'
[2025-08-12 12:08:53,241] INFO in action_factory: Registered action handler for 'waitElement'
[2025-08-12 12:08:53,241] INFO in action_factory: Registered action handler for 'compareValue'
[2025-08-12 12:08:53,241] INFO in action_factory: Registered action handler for 'deviceBack'
[2025-08-12 12:08:53,241] INFO in action_factory: Special case: Registering check_if_exists_action.py as 'exists'
[2025-08-12 12:08:53,241] INFO in action_factory: Registered action handler for 'exists'
[2025-08-12 12:08:53,242] INFO in action_factory: Registered action handler for 'clickElement'
[2025-08-12 12:08:53,242] INFO in action_factory: Registered action handler for 'randomData'
[2025-08-12 12:08:53,242] INFO in action_factory: Registered action handler for 'getValue'
[2025-08-12 12:08:53,242] INFO in action_factory: Registered action handler for 'test'
[2025-08-12 12:08:53,242] INFO in action_factory: Registered action handler for 'restartApp'
[2025-08-12 12:08:53,242] INFO in action_factory: Special case: Registering double_tap_action.py as 'doubleTap'
[2025-08-12 12:08:53,242] INFO in action_factory: Registered action handler for 'doubleTap'
[2025-08-12 12:08:53,242] INFO in action_factory: Registered action types: ['tap', 'wait', 'tapIfImageExists', 'multiStep', 'cleanupSteps', 'swipe', 'getParam', 'terminateApp', 'doubleClickImage', 'uninstallApp', 'text', 'waitTill', 'hookAction', 'inputText', 'setParam', 'repeatSteps', 'iosFunctions', 'swipeTillVisible', 'clickImage', 'takeScreenshot', 'tapIfLocatorExists', 'tapAndType', 'tapOnText', 'launchApp', 'ifThenSteps', 'info', 'waitElement', 'compareValue', 'deviceBack', 'exists', 'clickElement', 'randomData', 'getValue', 'test', 'restartApp', 'doubleTap']
[2025-08-12 12:08:53,242] INFO in action_factory: Handler for 'tap': TapAction
[2025-08-12 12:08:53,242] INFO in action_factory: Handler for 'wait': WaitAction
[2025-08-12 12:08:53,242] INFO in action_factory: Handler for 'tapIfImageExists': TapIfImageExistsAction
[2025-08-12 12:08:53,242] INFO in action_factory: Handler for 'multiStep': MultiStepAction
[2025-08-12 12:08:53,242] INFO in action_factory: Handler for 'cleanupSteps': CleanupStepsAction
[2025-08-12 12:08:53,242] INFO in action_factory: Handler for 'swipe': SwipeAction
[2025-08-12 12:08:53,243] INFO in action_factory: Handler for 'getParam': GetParamAction
[2025-08-12 12:08:53,243] INFO in action_factory: Handler for 'terminateApp': TerminateAppAction
[2025-08-12 12:08:53,243] INFO in action_factory: Handler for 'doubleClickImage': DoubleClickImageAction
[2025-08-12 12:08:53,243] INFO in action_factory: Handler for 'uninstallApp': UninstallAppAction
[2025-08-12 12:08:53,243] INFO in action_factory: Handler for 'text': TextAction
[2025-08-12 12:08:53,243] INFO in action_factory: Handler for 'waitTill': WaitTillAction
[2025-08-12 12:08:53,243] INFO in action_factory: Handler for 'hookAction': HookAction
[2025-08-12 12:08:53,243] INFO in action_factory: Handler for 'inputText': InputTextAction
[2025-08-12 12:08:53,243] INFO in action_factory: Handler for 'setParam': SetParamAction
[2025-08-12 12:08:53,243] INFO in action_factory: Handler for 'repeatSteps': RepeatStepsAction
[2025-08-12 12:08:53,243] INFO in action_factory: Handler for 'iosFunctions': IosFunctionsAction
[2025-08-12 12:08:53,243] INFO in action_factory: Handler for 'swipeTillVisible': SwipeTillVisibleAction
[2025-08-12 12:08:53,244] INFO in action_factory: Handler for 'clickImage': ClickImageAction
[2025-08-12 12:08:53,244] INFO in action_factory: Handler for 'takeScreenshot': TakeScreenshotAction
[2025-08-12 12:08:53,244] INFO in action_factory: Handler for 'tapIfLocatorExists': TapIfLocatorExistsAction
[2025-08-12 12:08:53,244] INFO in action_factory: Handler for 'tapAndType': TapAndTypeAction
[2025-08-12 12:08:53,244] INFO in action_factory: Handler for 'tapOnText': TapOnTextAction
[2025-08-12 12:08:53,244] INFO in action_factory: Handler for 'launchApp': LaunchAppAction
[2025-08-12 12:08:53,244] INFO in action_factory: Handler for 'ifThenSteps': IfThenStepsAction
[2025-08-12 12:08:53,244] INFO in action_factory: Handler for 'info': InfoAction
[2025-08-12 12:08:53,244] INFO in action_factory: Handler for 'waitElement': WaitElementAction
[2025-08-12 12:08:53,244] INFO in action_factory: Handler for 'compareValue': CompareValueAction
[2025-08-12 12:08:53,244] INFO in action_factory: Handler for 'deviceBack': DeviceBackAction
[2025-08-12 12:08:53,244] INFO in action_factory: Handler for 'exists': CheckIfExistsAction
[2025-08-12 12:08:53,244] INFO in action_factory: Handler for 'clickElement': ClickElementAction
[2025-08-12 12:08:53,244] INFO in action_factory: Handler for 'randomData': RandomDataAction
[2025-08-12 12:08:53,245] INFO in action_factory: Handler for 'getValue': GetValueAction
[2025-08-12 12:08:53,245] INFO in action_factory: Handler for 'test': TestAction
[2025-08-12 12:08:53,245] INFO in action_factory: Handler for 'restartApp': RestartAppAction
[2025-08-12 12:08:53,245] INFO in action_factory: Handler for 'doubleTap': DoubleTapAction
[2025-08-12 12:08:53,245] INFO in action_factory: Requested action type: 'tap', Available types: ['cleanupSteps', 'clickElement', 'clickImage', 'compareValue', 'deviceBack', 'doubleClickImage', 'doubleTap', 'exists', 'getParam', 'getValue', 'hookAction', 'ifThenSteps', 'info', 'inputText', 'iosFunctions', 'launchApp', 'multiStep', 'randomData', 'repeatSteps', 'restartApp', 'setParam', 'swipe', 'swipeTillVisible', 'takeScreenshot', 'tap', 'tapAndType', 'tapIfImageExists', 'tapIfLocatorExists', 'tapOnText', 'terminateApp', 'test', 'text', 'uninstallApp', 'wait', 'waitElement', 'waitTill']
[2025-08-12 12:08:53,245] INFO in action_factory: Action parameters before env resolution: {'method': 'locator', 'locator_type': 'accessibility_id', 'locator_value': 'txtHomeAccountCtaSignIn', 'timeout': 10, 'interval': 0.5}
[2025-08-12 12:08:53,245] INFO in action_factory: Applying environment variable resolution for env ID 7 to action params
[2025-08-12 12:08:53,245] INFO in action_factory: Action parameters after env resolution: {'method': 'locator', 'locator_type': 'accessibility_id', 'locator_value': 'txtHomeAccountCtaSignIn', 'timeout': 10, 'interval': 0.5}
[2025-08-12 12:08:53,246] INFO in tap_action: Attempting to tap using primary locator: accessibility_id=txtHomeAccountCtaSignIn
[2025-08-12 12:08:53,246] INFO in appium_device_controller: Tapping on element with accessibility_id='txtHomeAccountCtaSignIn' (timeout=10s, interval=0.5s)
[2025-08-12 12:08:53,246] INFO in appium_device_controller: Waiting for element to be clickable: accessibility_id='txtHomeAccountCtaSignIn'
[2025-08-12 12:08:56,116] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-08-12 12:08:56,116] INFO in appium_device_controller: Taking screenshot using native Appium driver (attempt 1/3)
[2025-08-12 12:08:56,745] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports_ios/testsuite_execution_20250812_120848/screenshots/latest.png
[2025-08-12 12:08:56,745] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-08-12 12:08:56,746] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:08:56] "GET /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1754964445684_6erufjb5r_1754956091087_t6hgnhoyw&t=************* HTTP/1.1" 200 -
[2025-08-12 12:08:57,399] INFO in appium_device_controller: Element found, tapping on it
[2025-08-12 12:08:59,027] INFO in player: Skipping delay after action execution for better performance
[2025-08-12 12:08:59,027] WARNING in player: Could not take screenshot after action: cannot access local variable 'i' where it is not associated with a value
[2025-08-12 12:08:59,027] INFO in player: DEBUG: Using test_idx from action: 0 for tracking completion
[2025-08-12 12:08:59,028] INFO in player: DEBUG: Updated player's current_test_idx to match action: 0
[2025-08-12 12:08:59,028] INFO in player: DEBUG: Updated global current_test_idx.value to match action: 0
[2025-08-12 12:08:59,028] INFO in player: DEBUG: Player instance ID: **********, has current_test_idx: True
[2025-08-12 12:08:59,028] INFO in player: DEBUG: Player's current_test_idx value: 0
[2025-08-12 12:08:59,028] INFO in player: DEBUG: Global current_test_idx.value: 0
[2025-08-12 12:08:59,028] INFO in player: ========== PLAYER COMPLETING ACTION WITH TEST_IDX: 0 ==========
[2025-08-12 12:08:59,028] INFO in player: ========== ACTION TYPE: tap ==========
[2025-08-12 12:08:59,028] INFO in player: ========== GLOBAL CURRENT_TEST_IDX: 0 ==========
[2025-08-12 12:08:59,029] INFO in player: ========== PLAYER CURRENT_TEST_IDX: 0 ==========
[2025-08-12 12:08:59,030] INFO in player: Tracked execution completion in database: test_idx=0, step_idx=2, action_type=tap, status=passed
[2025-08-12 12:09:00,030] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports_ios/testsuite_execution_20250812_120848/screenshots
[2025-08-12 12:09:00,031] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports_ios/testsuite_execution_20250812_120848/screenshots/screenshot_20250812_120900.png (save_debug=False)
[2025-08-12 12:09:00,031] INFO in appium_device_controller: Taking screenshot using native Appium driver (attempt 1/3)
[2025-08-12 12:09:00,323] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports_ios/testsuite_execution_20250812_120848/screenshots/latest.png
[2025-08-12 12:09:00,323] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250812_120900.png
[2025-08-12 12:09:00,326] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:09:00] "POST /api/action/execute HTTP/1.1" 200 -
[2025-08-12 12:09:00,332] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-08-12 12:09:00,333] INFO in appium_device_controller: Taking screenshot using native Appium driver (attempt 1/3)
[2025-08-12 12:09:00,545] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports_ios/testsuite_execution_20250812_120848/screenshots/latest.png
[2025-08-12 12:09:00,545] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-08-12 12:09:00,546] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:09:00] "GET /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1754964445684_6erufjb5r_1754956091087_t6hgnhoyw&t=1754964540328 HTTP/1.1" 200 -
[2025-08-12 12:09:00,549] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-08-12 12:09:00,549] INFO in appium_device_controller: Taking screenshot using native Appium driver (attempt 1/3)
[2025-08-12 12:09:00,768] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports_ios/testsuite_execution_20250812_120848/screenshots/latest.png
[2025-08-12 12:09:00,768] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-08-12 12:09:00,768] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:09:00] "GET /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1754964445684_6erufjb5r_1754956091087_t6hgnhoyw&t=1754964540328 HTTP/1.1" 200 -
[2025-08-12 12:09:00,850] INFO in _internal: 127.0.0.1 - - [12/Aug/2025 12:09:00] "GET /api/get_execution_context HTTP/1.1" 200 -
[2025-08-12 12:09:00,854] DEBUG in appium_device_controller: Session ID: 5f8d2829-2eb4-4b24-b9e3-89eabd4a567b
[2025-08-12 12:09:00,855] DEBUG in appium_device_controller: Session is responsive (session status check passed)
[2025-08-12 12:09:00,855] INFO in player: Executing action: {'action_id': 'yUJyVO5Wev', 'executionTime': '1286ms', 'function_name': 'alert_accept', 'timestamp': 1746099019334, 'type': 'iosFunctions', 'test_idx': 0}
[2025-08-12 12:09:00,855] INFO in player: DEBUG: Current test index at start of execute_action: 0
[2025-08-12 12:09:00,855] INFO in player: DEBUG: Player instance ID: **********, has current_test_idx: True
[2025-08-12 12:09:00,855] INFO in player: DEBUG: Player's current_test_idx value: 0
[2025-08-12 12:09:00,855] INFO in player: DEBUG: Using action_id from action: yUJyVO5Wev
[2025-08-12 12:09:00,855] INFO in player: DEBUG: Before tracking - current_test_idx.value: 0
[2025-08-12 12:09:00,855] INFO in player: DEBUG: Using test_idx from action: 0 for tracking
[2025-08-12 12:09:00,855] INFO in player: DEBUG: Updated player's current_test_idx to match action: 0
[2025-08-12 12:09:00,855] INFO in player: DEBUG: Updated global current_test_idx.value to match action: 0
[2025-08-12 12:09:00,855] INFO in player: ========== PLAYER EXECUTING ACTION WITH TEST_IDX: 0 ==========
[2025-08-12 12:09:00,855] INFO in player: ========== ACTION TYPE: iosFunctions ==========
[2025-08-12 12:09:00,855] INFO in player: ========== ACTION ID: yUJyVO5Wev ==========
[2025-08-12 12:09:00,855] INFO in player: ========== GLOBAL CURRENT_TEST_IDX: 0 ==========
[2025-08-12 12:09:00,855] INFO in player: ========== PLAYER CURRENT_TEST_IDX: 0 ==========
[2025-08-12 12:09:00,857] INFO in player: Tracked execution in database: test_idx=0, step_idx=3, action_type=iosFunctions, action_id=yUJyVO5Wev
[2025-08-12 12:09:00,857] INFO in player: Skipping device connection verification for better performance
[2025-08-12 12:09:00,857] INFO in action_factory: Registered basic actions: tap, wait
[2025-08-12 12:09:00,857] INFO in action_factory: Special case: Registering tap_if_image_exists_action.py as 'tapIfImageExists'
[2025-08-12 12:09:00,857] INFO in action_factory: Registered action handler for 'tapIfImageExists'
[2025-08-12 12:09:00,857] INFO in action_factory: Registered action handler for 'multiStep'
[2025-08-12 12:09:00,857] INFO in action_factory: Special case: Registering cleanup_steps_action.py as 'cleanupSteps'
[2025-08-12 12:09:00,857] INFO in action_factory: Registered action handler for 'cleanupSteps'
[2025-08-12 12:09:00,857] INFO in action_factory: Registered action handler for 'swipe'
[2025-08-12 12:09:00,857] INFO in action_factory: Registered action handler for 'getParam'
[2025-08-12 12:09:00,858] INFO in action_factory: Registered action handler for 'wait'
[2025-08-12 12:09:00,858] INFO in action_factory: Registered action handler for 'terminateApp'
[2025-08-12 12:09:00,858] INFO in action_factory: Registered action handler for 'doubleClickImage'
[2025-08-12 12:09:00,858] INFO in action_factory: Registered action handler for 'uninstallApp'
[2025-08-12 12:09:00,858] INFO in action_factory: Registered action handler for 'text'
[2025-08-12 12:09:00,861] ERROR in action_factory: Error loading action handler from tap_if_text_exists_action: expected 'except' or 'finally' block (tap_if_text_exists_action.py, line 170)
[2025-08-12 12:09:00,862] INFO in action_factory: Registered action handler for 'waitTill'
[2025-08-12 12:09:00,863] INFO in action_factory: Registered action handler for 'hookAction'
[2025-08-12 12:09:00,863] INFO in action_factory: Registered action handler for 'inputText'
[2025-08-12 12:09:00,863] INFO in action_factory: Registered action handler for 'setParam'
[2025-08-12 12:09:00,863] INFO in action_factory: Special case: Registering repeat_steps_action.py as 'repeatSteps'
[2025-08-12 12:09:00,863] INFO in action_factory: Registered action handler for 'repeatSteps'
[2025-08-12 12:09:00,863] INFO in action_factory: Registered action handler for 'iosFunctions'
[2025-08-12 12:09:00,864] INFO in action_factory: Registered action handler for 'swipeTillVisible'
[2025-08-12 12:09:00,864] INFO in action_factory: Registered action handler for 'clickImage'
[2025-08-12 12:09:00,864] INFO in action_factory: Registered action handler for 'tap'
[2025-08-12 12:09:00,864] INFO in action_factory: Special case: Registering take_screenshot_action.py as 'takeScreenshot'
[2025-08-12 12:09:00,864] INFO in action_factory: Registered action handler for 'takeScreenshot'
[2025-08-12 12:09:00,864] INFO in action_factory: Special case: Registering tap_if_locator_exists_action.py as 'tapIfLocatorExists'
[2025-08-12 12:09:00,864] INFO in action_factory: Registered action handler for 'tapIfLocatorExists'
[2025-08-12 12:09:00,864] INFO in action_factory: Registered action handler for 'tapAndType'
[2025-08-12 12:09:00,864] INFO in action_factory: Special case: Registering tap_on_text_action.py as 'tapOnText'
[2025-08-12 12:09:00,865] INFO in action_factory: Registered action handler for 'tapOnText'
[2025-08-12 12:09:00,865] INFO in action_factory: Registered action handler for 'launchApp'
[2025-08-12 12:09:00,865] INFO in action_factory: Registered action handler for 'ifThenSteps'
[2025-08-12 12:09:00,865] INFO in action_factory: Special case: Registering info_action.py as 'info'
[2025-08-12 12:09:00,865] INFO in action_factory: Registered action handler for 'info'
[2025-08-12 12:09:00,865] INFO in action_factory: Registered action handler for 'waitElement'
[2025-08-12 12:09:00,865] INFO in action_factory: Registered action handler for 'compareValue'
[2025-08-12 12:09:00,865] INFO in action_factory: Registered action handler for 'deviceBack'
[2025-08-12 12:09:00,865] INFO in action_factory: Special case: Registering check_if_exists_action.py as 'exists'
[2025-08-12 12:09:00,865] INFO in action_factory: Registered action handler for 'exists'
[2025-08-12 12:09:00,866] INFO in action_factory: Registered action handler for 'clickElement'
[2025-08-12 12:09:00,866] INFO in action_factory: Registered action handler for 'randomData'
[2025-08-12 12:09:00,866] INFO in action_factory: Registered action handler for 'getValue'
[2025-08-12 12:09:00,866] INFO in action_factory: Registered action handler for 'test'
[2025-08-12 12:09:00,866] INFO in action_factory: Registered action handler for 'restartApp'
[2025-08-12 12:09:00,866] INFO in action_factory: Special case: Registering double_tap_action.py as 'doubleTap'
[2025-08-12 12:09:00,866] INFO in action_factory: Registered action handler for 'doubleTap'
[2025-08-12 12:09:00,866] INFO in action_factory: Registered action types: ['tap', 'wait', 'tapIfImageExists', 'multiStep', 'cleanupSteps', 'swipe', 'getParam', 'terminateApp', 'doubleClickImage', 'uninstallApp', 'text', 'waitTill', 'hookAction', 'inputText', 'setParam', 'repeatSteps', 'iosFunctions', 'swipeTillVisible', 'clickImage', 'takeScreenshot', 'tapIfLocatorExists', 'tapAndType', 'tapOnText', 'launchApp', 'ifThenSteps', 'info', 'waitElement', 'compareValue', 'deviceBack', 'exists', 'clickElement', 'randomData', 'getValue', 'test', 'restartApp', 'doubleTap']
[2025-08-12 12:09:00,866] INFO in action_factory: Handler for 'tap': TapAction
[2025-08-12 12:09:00,866] INFO in action_factory: Handler for 'wait': WaitAction
[2025-08-12 12:09:00,866] INFO in action_factory: Handler for 'tapIfImageExists': TapIfImageExistsAction
[2025-08-12 12:09:00,866] INFO in action_factory: Handler for 'multiStep': MultiStepAction
[2025-08-12 12:09:00,866] INFO in action_factory: Handler for 'cleanupSteps': CleanupStepsAction
[2025-08-12 12:09:00,866] INFO in action_factory: Handler for 'swipe': SwipeAction
[2025-08-12 12:09:00,866] INFO in action_factory: Handler for 'getParam': GetParamAction
[2025-08-12 12:09:00,866] INFO in action_factory: Handler for 'terminateApp': TerminateAppAction
[2025-08-12 12:09:00,866] INFO in action_factory: Handler for 'doubleClickImage': DoubleClickImageAction
[2025-08-12 12:09:00,866] INFO in action_factory: Handler for 'uninstallApp': UninstallAppAction
[2025-08-12 12:09:00,867] INFO in action_factory: Handler for 'text': TextAction
[2025-08-12 12:09:00,867] INFO in action_factory: Handler for 'waitTill': WaitTillAction
[2025-08-12 12:09:00,867] INFO in action_factory: Handler for 'hookAction': HookAction
[2025-08-12 12:09:00,867] INFO in action_factory: Handler for 'inputText': InputTextAction
[2025-08-12 12:09:00,867] INFO in action_factory: Handler for 'setParam': SetParamAction
[2025-08-12 12:09:00,867] INFO in action_factory: Handler for 'repeatSteps': RepeatStepsAction
[2025-08-12 12:09:00,867] INFO in action_factory: Handler for 'iosFunctions': IosFunctionsAction
[2025-08-12 12:09:00,867] INFO in action_factory: Handler for 'swipeTillVisible': SwipeTillVisibleAction
[2025-08-12 12:09:00,867] INFO in action_factory: Handler for 'clickImage': ClickImageAction
[2025-08-12 12:09:00,867] INFO in action_factory: Handler for 'takeScreenshot': TakeScreenshotAction
[2025-08-12 12:09:00,867] INFO in action_factory: Handler for 'tapIfLocatorExists': TapIfLocatorExistsAction
[2025-08-12 12:09:00,868] INFO in action_factory: Handler for 'tapAndType': TapAndTypeAction
[2025-08-12 12:09:00,868] INFO in action_factory: Handler for 'tapOnText': TapOnTextAction
[2025-08-12 12:09:00,868] INFO in action_factory: Handler for 'launchApp': LaunchAppAction
[2025-08-12 12:09:00,868] INFO in action_factory: Handler for 'ifThenSteps': IfThenStepsAction
[2025-08-12 12:09:00,868] INFO in action_factory: Handler for 'info': InfoAction
[2025-08-12 12:09:00,868] INFO in action_factory: Handler for 'waitElement': WaitElementAction
[2025-08-12 12:09:00,868] INFO in action_factory: Handler for 'compareValue': CompareValueAction
[2025-08-12 12:09:00,868] INFO in action_factory: Handler for 'deviceBack': DeviceBackAction
[2025-08-12 12:09:00,868] INFO in action_factory: Handler for 'exists': CheckIfExistsAction
[2025-08-12 12:09:00,868] INFO in action_factory: Handler for 'clickElement': ClickElementAction
[2025-08-12 12:09:00,868] INFO in action_factory: Handler for 'randomData': RandomDataAction
[2025-08-12 12:09:00,868] INFO in action_factory: Handler for 'getValue': GetValueAction
[2025-08-12 12:09:00,869] INFO in action_factory: Handler for 'test': TestAction
[2025-08-12 12:09:00,869] INFO in action_factory: Handler for 'restartApp': RestartAppAction
[2025-08-12 12:09:00,869] INFO in action_factory: Handler for 'doubleTap': DoubleTapAction
[2025-08-12 12:09:00,869] INFO in player: Forwarding iOS Functions action to ActionFactory with params: {'function_name': 'alert_accept'}
[2025-08-12 12:09:00,869] INFO in action_factory: Requested action type: 'iosFunctions', Available types: ['cleanupSteps', 'clickElement', 'clickImage', 'compareValue', 'deviceBack', 'doubleClickImage', 'doubleTap', 'exists', 'getParam', 'getValue', 'hookAction', 'ifThenSteps', 'info', 'inputText', 'iosFunctions', 'launchApp', 'multiStep', 'randomData', 'repeatSteps', 'restartApp', 'setParam', 'swipe', 'swipeTillVisible', 'takeScreenshot', 'tap', 'tapAndType', 'tapIfImageExists', 'tapIfLocatorExists', 'tapOnText', 'terminateApp', 'test', 'text', 'uninstallApp', 'wait', 'waitElement', 'waitTill']
[2025-08-12 12:09:00,869] INFO in action_factory: Action parameters before env resolution: {'function_name': 'alert_accept'}
[2025-08-12 12:09:00,869] INFO in action_factory: Applying environment variable resolution for env ID 7 to action params
[2025-08-12 12:09:00,870] INFO in action_factory: Action parameters after env resolution: {'function_name': 'alert_accept'}
[2025-08-12 12:09:00,870] INFO in ios_functions_action: Executing iOS function: alert_accept
[2025-08-12 12:09:02,011] INFO in player: Skipping delay after action execution for better performance
[2025-08-12 12:09:02,011] WARNING in player: Could not take screenshot after action: cannot access local variable 'i' where it is not associated with a value
[2025-08-12 12:09:02,011] INFO in player: DEBUG: Using test_idx from action: 0 for tracking completion
[2025-08-12 12:09:02,012] INFO in player: DEBUG: Updated player's current_test_idx to match action: 0
[2025-08-12 12:09:02,012] INFO in player: DEBUG: Updated global current_test_idx.value to match action: 0
[2025-08-12 12:09:02,012] INFO in player: DEBUG: Player instance ID: **********, has current_test_idx: True
[2025-08-12 12:09:02,012] INFO in player: DEBUG: Player's current_test_idx value: 0
[2025-08-12 12:09:02,012] INFO in player: DEBUG: Global current_test_idx.value: 0
[2025-08-12 12:09:02,012] INFO in player: ========== PLAYER COMPLETING ACTION WITH TEST_IDX: 0 ==========
[2025-08-12 12:09:02,012] INFO in player: ========== ACTION TYPE: iosFunctions ==========
[2025-08-12 12:09:02,012] INFO in player: ========== GLOBAL CURRENT_TEST_IDX: 0 ==========
[2025-08-12 12:09:02,013] INFO in player: ========== PLAYER CURRENT_TEST_IDX: 0 ==========
[2025-08-12 12:09:02,014] INFO in player: Tracked execution completion in database: test_idx=0, step_idx=3, action_type=iosFunctions, status=passed
